import { FightEntity } from './../entities/fight.entity';
import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GeneralEntity } from 'src/entities/general.entity';
import { RoleEntity } from 'src/entities/role.entity';
import { DataSource, Equal, In, MoreThan, Repository } from 'typeorm';
import { checkMaxLevel, equipmentType, formatDuring, generalTemplate, getGoodType, getHpByLevel, goodsType, goodsType1, handelfn, hunpofn, isPureNumber, jingmaiFn, roleUpgrade, soldierTemplate, weaponType } from 'src/utils/config'
import { SoldierEntity } from 'src/entities/soldier.entity';
import { SkillEntity } from 'src/entities/skill.entity';
import { ManorEventEntity } from 'src/entities/manorEvent.entity';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CommonService } from 'src/middleware/common.service';
import { TasksService } from 'src/tasks/tasks.service';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { EquipEntity } from 'src/entities/equip.entity';
import { GcmdService } from './gcmd.service';
import { GoodsService } from './goods.service';
import { EquipmentEntity } from 'src/entities/equipment/equipment.entity';
import { GeneralEquip } from 'src/entities/general/generalequip.entity';
import { RedisService } from 'src/middleware/redis.service';
import { BlessEntity } from 'src/entities/general/bless.entity';
import { GeneralotherService } from './generalother.service';
import { MountEntity } from 'src/entities/general/mount.entity';
import { generalAttrHandel, generalEquip, mountConfig } from 'src/utils/config1';
import { OtherService } from './config/other.service';
import { OtherSeaService } from './config/otherSea.service';
@Injectable()
//我的武将
export class GeneralService {
    private readonly serviceName = 'general'
    private readonly size = 20
    private nearBygeneralNum = 4;//近身武将数量
    constructor(
        private readonly commonService: CommonService,
        private readonly tasksService: TasksService,
        private readonly goodsService: GoodsService,
        private readonly otherSeaService: OtherSeaService,
        @Inject(forwardRef(() => OtherService))private readonly otherService: OtherService,
        @Inject(forwardRef(() => GeneralotherService)) private readonly generalotherService: GeneralotherService,
        @Inject(forwardRef(() => GcmdService)) private readonly gcmd: GcmdService,
        private eventEmitter: EventEmitter2,
        private readonly redis: RedisService,
        @InjectRepository(GeneralEntity) private readonly generalEntity: Repository<GeneralEntity>,
        @InjectRepository(MountEntity) private readonly mountEntity: Repository<MountEntity>,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(SoldierEntity) private readonly soldierEntity: Repository<SoldierEntity>,
        @InjectRepository(SkillEntity) private readonly skillEntity: Repository<SkillEntity>,
        @InjectRepository(BlessEntity) private readonly blessEntity: Repository<BlessEntity>,

        @InjectRepository(EquipmentEntity) private readonly equipmentEntity: Repository<EquipmentEntity>,//装备
        @InjectRepository(GeneralEquip) private readonly generalEquipEntity: Repository<GeneralEquip>,
        @InjectRepository(ManorEventEntity) private readonly manorEventEntity: Repository<ManorEventEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        @InjectRepository(EquipEntity) private readonly equipEntity: Repository<EquipEntity>,
        @InjectRepository(FightEntity) private readonly fightEntity: Repository<FightEntity>,
        private dataSource: DataSource,
    ) { }
    //武将列表 checkNear操作近身  gjlc 供给粮草
    async generalList(sid: string, cmd: number, userId: string, { checkNear, generalId, gjlc }) {
        let msg = '';
        if (checkNear) {
            msg = await this.setAndCancelNear(generalId, userId)
        }
        if (gjlc) {
            let userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
            if (userInfo.food < 8) {
                msg = `你粮草不足</br>`
            } else {
                await this.dataSource.transaction(async (manager) => {
                    await manager.update(GeneralEntity, { id: Number(generalId) }, { hpNow: () => 'hp' })
                    await manager.update(RoleEntity, { id: Number(userId) }, { food: () => 'food -' + 8})
                })
                let general = await this.getGeneralDetail(generalId)
                this.eventEmitter.emit('writeLogs', { userId, name: '供给粮草:' + general.name, food: 8 })
                msg = `你供给粮草x8给${general.name}</br>`
            }
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'generalList', title: '武将列表', service: this.serviceName, params: { generalId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let generalList = await this.getGeneralList(userId)
        let str = ''
        generalList.forEach((item, index) => {
            str += `<a href="${this.seturlOther(params, urlObj, item.name, 'generalInfo', { generalId: item.id })}">${index + 1}.${item.name}${item.near ? '(近身)' : ''}</a>
            [<a href="${this.seturlOther(params, urlObj, '武将列表', 'generalList', { checkNear: 1, generalId: item.id })}">${item.near ? '撤销近身' : '设为近身'}</a>]
            ${item.hpNow !=item.hp ? '[<a href="' + this.seturlOther(params, urlObj, '武将列表', 'generalList', { gjlc: 1, generalId: item.id }) + '">供给粮草</a>]' : ''}<br/>`
        });
        if(generalList.length==0){
            await this.addGeneralAll(userId,'zhouwushi')
        }
        //设置返回
        let content = `${msg}你的武将有：<br/>
        ${str}
        <a href="${this.seturlOther(params, urlObj, '我的军队', 'armyList')}">我的军队</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //撤销、设置近身
    async setAndCancelNear(generalId: number, userId) {
        let generalList = await this.getGeneralList(userId)
        generalList = generalList.filter(item => item.near)
        if (generalList.some(item => item.id == generalId)) {
            //撤销近身 
            let users = []
            generalList.filter(item => item.id != generalId).sort((a, b) => a.near - b.near).forEach((item, index) => {
                users.push({ id: item.id, near: index+1 })
            })
            users.push({ id: generalId, near: 0 })
            let str = await this.generalEntity.save(users)
            if (str) {
                return `你撤销了近身武将：${generalList.find(item => item.id == generalId).name}</br>`
            }
        } else {
            if (generalList.length >= this.nearBygeneralNum) return '你设置的近身武将数量已达到上限!</br>'
            let str = await this.generalEntity.save({ id: generalId, near: generalList.length + 1 })
            if (str) {
                let generalInfo = await this.getGeneralDetail(generalId)
                return `你设置${generalInfo.name}为近身武将</br>`
            }
        }
    }
    //军队列表
    async armyList(sid: string, cmd: number, userId: string, { checkNear, generalId }) {
        let msg = '';
        if (checkNear) {
            let msg = await this.setAndCancelNear(generalId, userId)
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'armyList', title: '军队列表', service: 'general', params: { checkNear, generalId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let generalList = await this.getGeneralList(userId)
        let str = ''
        generalList.forEach((item, index) => {
            str += `<a href="${this.seturlOther(params, urlObj, item.name, 'armyGeneral', { generalId: item.id })}">${index + 1}.${item.name}部${item.near ? '(近身)' : ''}</a>[<a href="${this.seturlOther(params, urlObj, '军队列表', 'armyList', { checkNear: 1, generalId: item.id })}">${item.near ? '撤销近身' : '设为近身'}</a>]<br/>`
        });
        for (let index = 0; index < generalList.length; index++) {
        }
        //设置返回
        let content = `${msg}你的军队有：<br/>
        ${str}
        <a href="${this.seturlOther(params, urlObj, '散部', 'sanbuSoldierList', {},'generalother')}">散部</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //军队-武将详情
    async armyGeneral(sid: string, cmd: number, userId: string, { checkNear, generalId }) {
        //判断是不是自己的武将
        let msg = '';
        if (checkNear) {
            msg = await this.setAndCancelNear(generalId, userId)
        }
        let generalInfo = await this.getGeneralDetail(generalId)
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'armyGeneral', title: generalInfo.name, service: 'general', params: { generalId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let team = [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]];//人口、种类
        generalInfo.soldiers.forEach((item, index) => {
            team[item.teamOrder - 1][0] += item.count
            team[item.teamOrder - 1][1] += 1
        })
        team[generalInfo.teamOrder - 1][0] += 1
        team[generalInfo.teamOrder - 1][1] += 1
        let str=''
        if(generalInfo.userId==Number(userId)){
            str=`<a href="${this.seturlOther(params, urlObj, generalInfo.name + '部', 'armyGeneral', { checkNear: 1, generalId: generalInfo.id })}">${generalInfo.near ? '撤销' : '设为'}近身武将</a><br/>`
        }else{
            //攻击此军队
        }
        let content = `${msg}${generalInfo.name}部${generalInfo.near ? '(近身)' : ''}<br/>
        武将:<a href="${this.seturlOther(params, urlObj, generalInfo.name, 'generalInfo', { generalId: generalInfo.id })}">${generalInfo.name}</a><br/>
        士兵:0<br/>
        <a href="${this.seturlOther(params, urlObj, '第一梯队', 'armyGeneralDeatail', { tierId: 1, generalId: generalInfo.id })}">第一梯队(${team[0][0]}人/${team[0][1]}种)</a><br/>
        <a href="${this.seturlOther(params, urlObj, '第二梯队', 'armyGeneralDeatail', { tierId: 2, generalId: generalInfo.id })}">第二梯队(${team[1][0]}人/${team[1][1]}种)</a><br/>
        <a href="${this.seturlOther(params, urlObj, '第三梯队', 'armyGeneralDeatail', { tierId: 3, generalId: generalInfo.id })}">第三梯队(${team[2][0]}人/${team[2][1]}种)</a><br/>
        <a href="${this.seturlOther(params, urlObj, '指挥部', 'armyGeneralDeatail', { tierId: 4, generalId: generalInfo.id })}">指挥部(${team[3][0]}人/${team[3][1]}种)</a><br/>
        <a href="${this.seturlOther(params, urlObj, '散队', 'armyGeneralDeatail', { tierId: 5, generalId: generalInfo.id })}">散队士兵(${team[4][0]}人/${team[4][1]}种)</a><br/>
        ${str}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //军队-梯队详情 参数有 武将id 和 梯队id 目标id 调整到梯队id   后三个是可选参数 调整梯队的时候使用 是否武将 1是 2不是
    async armyGeneralDeatail(sid: string, cmd: number, userId: string, { generalId, tierId, infoId, toId, isGeneral }) {
        let msg = '';
        //处理调整梯队逻辑
        if (infoId && toId) {
            if (isGeneral == 1) {
                //调整武将
                await this.generalEntity.update({ id: infoId }, { teamOrder: toId })
            } else {
                //调整士兵
                await this.soldierEntity.update({ id: infoId }, { teamOrder: toId })
            }
            msg = '调整梯队成功</br>'
        }
        let tierArr = ['第一梯队', '第二梯队', '第三梯队', '指挥部', '散队']
        let generalInfo = await this.getGeneralDetail(generalId)
        let isSelfTeam = generalInfo.userId == Number(userId)
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'armyGeneralDeatail', title: tierArr[tierId - 1], service: 'general', params: { generalId, tierId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let str = ''
        if(generalInfo.teamOrder == tierId){
            str = `<a href="${this.seturlOther(params, urlObj, generalInfo.name, 'generalInfo', { generalId: generalInfo.id })}">${generalInfo.name}</a> `
            if(isSelfTeam){
                str += `<a href="${this.seturlOther(params, urlObj, '调离梯队', 'armyAdjust', { generalId: generalInfo.id, infoId: generalInfo.id, fromTierId: tierId, isGeneral: 1 })}">调离梯队</a>`
            }
            str += '<br/>'
        }
        generalInfo.soldiers.filter(item => item.teamOrder == tierId).forEach((item, index) => {
            str += `<a href="${this.seturlOther(params, urlObj, item.name, 'soldierInfo', { soldierId: item.id })}">${item.name}(${item.count}人)</a> `
            if(isSelfTeam){
                str += `<a href="${this.seturlOther(params, urlObj, '调离梯队', 'armyAdjust', { generalId: generalInfo.id, infoId: item.id, fromTierId: tierId, isGeneral: 2 })}">调离梯队</a>`
            }
            str+='<br/>'
        })
        let content = `${msg}${generalInfo.name}部${tierArr[tierId - 1]}士兵有：<br/>
        ${str}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //军队-士兵调整梯队 武将id  要调整的武将/士兵id  梯队id
    async armyAdjust(sid: string, cmd: number, userId: string, { generalId, infoId, fromTierId, isGeneral }) {
        let tierArr = ['第一梯队', '第二梯队', '第三梯队', '指挥部', '散队']
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'armyAdjust', title: '调整梯队', service: 'general', params: { generalId, infoId, fromTierId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `请选择目标队列：<br/>
            <a href="${this.seturlOther(params, urlObj, tierArr[fromTierId - 1], 'armyGeneralDeatail', { generalId, tierId: fromTierId, infoId, toId: 1, isGeneral })}">第一梯队</a><br/>
            <a href="${this.seturlOther(params, urlObj, tierArr[fromTierId - 1], 'armyGeneralDeatail', { generalId, tierId: fromTierId, infoId, toId: 2, isGeneral })}">第二梯队</a><br/>
            <a href="${this.seturlOther(params, urlObj, tierArr[fromTierId - 1], 'armyGeneralDeatail', { generalId, tierId: fromTierId, infoId, toId: 3, isGeneral })}">第三梯队</a><br/>
            <a href="${this.seturlOther(params, urlObj, tierArr[fromTierId - 1], 'armyGeneralDeatail', { generalId, tierId: fromTierId, infoId, toId: 4, isGeneral })}">指挥部</a><br/>
            <a href="${this.seturlOther(params, urlObj, tierArr[fromTierId - 1], 'armyGeneralDeatail', { generalId, tierId: fromTierId, infoId, toId: 5, isGeneral })}">散队</a><br/>
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //武将装备详情页
    async generalEquipDetail(sid: string, cmd: number, userId: string, { generalId,equipId=null,removeEquipId=null,itemtype=null }) {
        let msg = '',pageTitle='装备详情'
        //穿上装备
        const zhuangbeifn=async (equipId)=>{
            msg='装备失败了</br>'
            let goodInfo=await this.personGoodsEntity.findOne({ where: { id:equipId} ,relations:['good'] })
            let title,id;
            if(goodInfo.good.type==2){
                title=generalEquip['2'].title
                id=generalEquip['2'].id
            }else{
                title=generalEquip[goodInfo.good.subType].title
                id=generalEquip[goodInfo.good.subType].id
            }
            await this.dataSource.transaction(async (manager) => {
                let generalInfo = await manager.findOne(GeneralEntity, { where: { id: Number(generalId) } })
                //如果已穿戴装备 就替换掉
                if(generalInfo[title]){
                    let goodInfo1=await this.personGoodsEntity.findOne({ where: { id:generalInfo[id]} ,relations:['good'] })
                    goodInfo1.count+=1
                    goodInfo1.usedNum-=1
                    await manager.save(goodInfo1)
                }
                await manager.update(GeneralEntity,{id:Number(generalId)},{[title]:goodInfo.good.name,[id]:goodInfo.id} )
                goodInfo.count-=1
                goodInfo.usedNum+=1
                await manager.save(goodInfo)
                msg='装备成功</br>'
            })
            await this.calculatePower('general', generalId)
        }
        if(equipId){
            await zhuangbeifn(equipId)
            this.goodsService.updateUserWeight(Number(userId))
        }
        if(removeEquipId){
            msg='卸下装备失败了</br>'
            let goodInfo=await this.personGoodsEntity.findOne({ where: { id:removeEquipId} ,relations:['good'] })
            let title,id;
            if(goodInfo.good.type==2){
                title=generalEquip['2'].title
                id=generalEquip['2'].id
            }else{
                title=generalEquip[goodInfo.good.subType].title
                id=generalEquip[goodInfo.good.subType].id
            }
            await this.dataSource.transaction(async (manager) => {
                await manager.update(GeneralEntity,{id:Number(generalId)},{[title]:null,[id]:null} )
                goodInfo.count+=1
                goodInfo.usedNum-=1
                await manager.save(goodInfo)
                msg='卸下装备成功</br>'
            })
            this.goodsService.updateUserWeight(Number(userId))
            await this.calculatePower('general', generalId)
        }
        //修理装备
        if(itemtype=='xiuli'){
            let generalInfo = await this.generalEntity.findOne({where:{id:Equal(generalId)}})
            let count=0
            let keyArr=Object.keys(generalEquip)
            let idArr=[]
            for (let index = 0; index < keyArr.length; index++) {
                let element=generalEquip[keyArr[index]]
                if(generalInfo[element.id]){
                    let count1=await this.goodsService.repairEquip(generalInfo[element.id])
                    if(count1>0){
                        idArr.push(generalInfo[element.id])
                        count+=(await this.goodsService.repairEquip(generalInfo[element.id]))
                    }
                }
            }
            if(count>0){
                await this.dataSource.transaction(async (entityManager) => {
                    let goodInfoList=await this.personGoodsEntity.find({where:{id:In(idArr)},relations:['good']})
                    let userInfo=await this.roleEntity.findOne({where:{id:Number(userId)}})
                    if(userInfo.gold<count){
                        msg=`修理装备需要${count}银两，银两不足，修理失败</br>`
                    }else{
                        userInfo.gold-=count
                        await entityManager.save(userInfo)
                        goodInfoList.forEach(element => {
                            element.durability=element.good.durability
                        });
                        await entityManager.save(goodInfoList)
                        msg=`你花费${count}银两修理装备</br>`
                        this.eventEmitter.emit('writeLogs', { userId, name: '修理装备:' + generalInfo.name, gold: count })
                    }
                })
            }else{
                msg=`没有需要修理的装备</br>`
            }
        }
        //自动换装
        if(itemtype=='huanzhuang'){
            let personGoodList=await this.personGoodsEntity.find({where:{userId:Number(userId),good:{type:In([2,3])},count:MoreThan(0)},relations:['good'],select:['id','good','count','attack','defense']})
            personGoodList=personGoodList.sort((a,b)=>(b.attack+b.defense)-(a.attack+a.defense))
            for (const key in generalEquip) {
                let type=key=='2'?2:3,
                subType=key=='2'?['21','22','23','24','25','26']:[key]
                let goodInfo=personGoodList.find(item=>{
                    return item.good.type==type&&subType.includes(String(item.good.subType))
                })
                if(goodInfo){
                    await zhuangbeifn(goodInfo.id)
                }
            }
            msg=`自动换装成功</br>`
            this.goodsService.updateUserWeight(Number(userId))
        }
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {generalId},pageTitle)
        let generalInfo = await this.generalEntity.findOne({where:{id:Equal(generalId)}})
        let contentStr=''
        Object.keys(generalEquip).forEach(item=>{
            let element=generalEquip[item]
            contentStr+=`${element.name}:`
            if(generalInfo[element.id]){
                contentStr+=`<a href="${this.seturlOther(params, urlObj, '装备详情', 'wearingEquip', { id:generalInfo[element.id] },'general')}">${generalInfo[element.title]}</a> 
                <a href="${this.seturlOther(params, urlObj, '卸下', 'generalEquipDetail', { generalId,removeEquipId:generalInfo[element.id] })}">卸下</a>`
            }else{
                contentStr+=`<a href="${this.seturlOther(params, urlObj, '选择装备', 'checkEquip', { type:(Number(item)==2)?2:3,subType:Number(item)==2?null:Number(item), itemId: generalId },'generalother')}">选择</a>`
            }
            contentStr+='<br/>'
        })
        let content = `${msg}${generalInfo.name}的装备：<br/>
        ${contentStr}
        <a href="${this.seturlOther(params, urlObj, pageTitle, 'generalEquipDetail', {generalId,itemtype:'huanzhuang'})}">自动换装</a><br/>
        <a href="${this.seturlOther(params, urlObj, pageTitle, 'generalEquipDetail', {generalId,itemtype:'xiuli'})}">修理所有</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //士兵装备详情页
    async soldierEquipDetail(sid: string, cmd: number, userId: string, { soldierId,equipId=null,removeEquipId=null,itemtype=null }) {
        let msg = '',pageTitle='装备详情'
        //穿上装备
        const zhuangbeifn=async (equipId)=>{
            msg='装备失败了</br>'
            let goodInfo=await this.personGoodsEntity.findOne({ where: { id:equipId} ,relations:['good'] })
            let title='bingqiName',id='bingqiId';
            if(goodInfo.good.type!=4){
                title=generalEquip[goodInfo.good.subType].title
                id=generalEquip[goodInfo.good.subType].id
            }
            await this.dataSource.transaction(async (manager) => {
                let generalInfo = await manager.findOne(SoldierEntity, { where: { id: Number(soldierId) } })
                //如果已穿戴装备 就替换掉
                if(generalInfo[title]){
                    let goodInfo1=await this.personGoodsEntity.findOne({ where: { id:generalInfo[id]} ,relations:['good'] })
                    goodInfo1.count+=1
                    goodInfo1.usedNum-=1
                    await manager.save(goodInfo1)
                }
                await manager.update(SoldierEntity,{id:Number(soldierId)},{[title]:goodInfo.good.name,[id]:goodInfo.id} )
                goodInfo.count-=1
                goodInfo.usedNum+=1
                await manager.save(goodInfo)
                msg='装备成功</br>'
            })
            await this.calculatePower('soldier', soldierId)
        }
        if(equipId){
            await zhuangbeifn(equipId)
            this.goodsService.updateUserWeight(Number(userId))
        }
        if(removeEquipId){
            msg='卸下装备失败了</br>'
            let goodInfo=await this.personGoodsEntity.findOne({ where: { id:removeEquipId} ,relations:['good'] })
            let title='bingqiName',id='bingqiId';
            if(goodInfo.good.type!=4){
                title=generalEquip[goodInfo.good.subType].title
                id=generalEquip[goodInfo.good.subType].id
            }
            await this.dataSource.transaction(async (manager) => {
                await manager.update(SoldierEntity,{id:Number(soldierId)},{[title]:null,[id]:null} )
                goodInfo.count+=1
                goodInfo.usedNum-=1
                await manager.save(goodInfo)
                msg='卸下装备成功</br>'
            })
            this.goodsService.updateUserWeight(Number(userId))
            await this.calculatePower('soldier', soldierId)
        }
        //修理装备
        if(itemtype=='xiuli'){
            let soldierInfo = await this.soldierEntity.findOne({where:{id:Equal(soldierId)}})
            let count=0
            let keyArr=Object.keys(generalEquip)
            let idArr=[]
            for (let index = 0; index < keyArr.length; index++) {
                let element=generalEquip[keyArr[index]]
                if(soldierInfo[element.id]){
                    let count1=await this.goodsService.repairEquip(soldierInfo[element.id])
                    if(count1>0){
                        idArr.push(soldierInfo[element.id])
                        count+=(await this.goodsService.repairEquip(soldierInfo[element.id]))
                    }
                }
            }
            if(count>0){
                await this.dataSource.transaction(async (entityManager) => {
                    let goodInfoList=await this.personGoodsEntity.find({where:{id:In(idArr)},relations:['good']})
                    let userInfo=await this.roleEntity.findOne({where:{id:Number(userId)}})
                    if(userInfo.gold<count){
                        msg=`修理装备需要${count}银两，银两不足，修理失败</br>`
                    }else{
                        userInfo.gold-=count
                        await entityManager.save(userInfo)
                        goodInfoList.forEach(element => {
                            element.durability=element.good.durability
                        });
                        await entityManager.save(goodInfoList)
                        msg=`你花费${count}银两修理装备</br>`
                        this.eventEmitter.emit('writeLogs', { userId, name: '修理装备:' + soldierInfo.name, gold: count })
                    }
                })
            }else{
                msg=`没有需要修理的装备</br>`
            }
        }
        //自动换装
        if(itemtype=='huanzhuang'){
            let personGoodList=await this.personGoodsEntity.find({where:{userId:Number(userId),good:{type:In([4,5])},count:MoreThan(0)},relations:['good'],select:['id','good','count','attack','defense']})
            personGoodList=personGoodList.sort((a,b)=>(b.attack+b.defense)-(a.attack+a.defense))
            for (const key in generalEquip) {
                let type=key=='2'?4:5,
                subType=key=='2'?['21','22','23','24','25','26']:[key]
                let goodInfo=personGoodList.find(item=>{
                    return item.good.type==type&&subType.includes(String(item.good.subType))
                })
                if(goodInfo){
                    await zhuangbeifn(goodInfo.id)
                }
            }
            msg=`自动换装成功</br>`
            this.goodsService.updateUserWeight(Number(userId))
            await this.calculatePower('soldier', soldierId)
        }
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {soldierId},pageTitle)
        let soldierInfo = await this.soldierEntity.findOne({where:{id:Equal(soldierId)}})
        let contentStr=''
        Object.keys(generalEquip).forEach(item=>{
            let element=generalEquip[item]
            contentStr+=`${element.name}:`
            if(soldierInfo[element.id]){
                contentStr+=`<a href="${this.seturlOther(params, urlObj, '装备详情', 'wearingEquip', { id:soldierInfo[element.id] },'general')}">${soldierInfo[element.title]}</a> 
                <a href="${this.seturlOther(params, urlObj, '卸下', 'soldierEquipDetail', { soldierId,removeEquipId:soldierInfo[element.id] })}">卸下</a>`
            }else{
                contentStr+=`<a href="${this.seturlOther(params, urlObj, '选择装备', 'checkEquip', { type:(Number(item)==2)?4:5,subType:Number(item)==2?null:Number(item), itemId: soldierId },'generalother')}">选择</a>`
            }
            contentStr+='<br/>'
        })
        let content = `${msg}${soldierInfo.name}的装备：<br/>
        ${contentStr}
        <a href="${this.seturlOther(params, urlObj, pageTitle, 'soldierEquipDetail', {soldierId,itemtype:'huanzhuang'})}">自动换装</a><br/>
        <a href="${this.seturlOther(params, urlObj, pageTitle, 'soldierEquipDetail', {soldierId,itemtype:'xiuli'})}">修理所有</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //武将/士兵装备 personGoodId穿装备  equipId卸装备
    async generalEquip(sid: string, cmd: number, userId: string, { generalId, soldierId,equipId,removeEquipId,itemtype=null }) {
        let msg = ''
        if(equipId){
            msg=await this.generalotherService.equipChange(1, equipId, generalId||soldierId)
        }
        if(removeEquipId){
            msg=await this.generalotherService.equipChange(2, removeEquipId, Number(userId))
        }
        //更新战力
        let type = 'general', itemId = generalId;
        if (soldierId) {
            type = 'soldier', itemId = soldierId;
        }
        
        //武将详情
        let info = generalId ? await this.getGeneralDetail(generalId) : await this.getSoldierDetail(soldierId)
        await this.calculatePower(type, itemId)
        // if (personGoodId || equipId) {
        //     info = generalId ? await this.getGeneralDetail(generalId) : await this.getSoldierDetail(soldierId)
        // }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'generalEquip', title: info.name + '的装备', service: 'general', params: { generalId, soldierId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let params1={}
        generalId&&(params1['generalId']=generalId)
        soldierId&&(params1['soldierId']=soldierId)
        let equipment=await this.generalEquipEntity.find({where:params1});//获取到所有装备
        let contentStr='';
        for (let index = 0; index < equipmentType.length; index++) {
            const element = equipmentType[index];
            let str=''
            let genGood=equipment.find(item=>item.type==element.id)
            if(element.id==30){
                genGood=equipment.find(item=>([21,22,23,24,25,26].includes(item.type)))
            }
            if(genGood){
                let goodInfo=await this.goodsService.getGoodDetail(genGood.equipmentId)
                str=`<a href="${this.seturlOther(params, urlObj, info.name + '的装备', 'wearingEquip', { id: genGood.id })}">${goodInfo.name}</a> 
                <a href="${this.seturlOther(params, urlObj, info.name + '的装备', 'generalEquip', { generalId, soldierId, removeEquipId: genGood.id })}">卸下</a>`
            }else{
                let type:number,subType:number;
                if(element.id==30){
                    type=generalId?2:4
                }else{
                    type=generalId?3:5
                }
                let params1={
                    type,
                    subType:element.id!=30?element.id:null,
                    itemId:generalId||soldierId,
                }
                str=`<a href="${this.seturlOther(params, urlObj, '选择装备', 'checkEquip',params1,'generalother')}">选择</a>`
            }
            contentStr+=`${element.name}:${str}<br/>`
        }
        let content = `${msg}${info.name}的装备：<br/>
            ${contentStr}
            <a href="">自动换装</a><br/>
            <a href="">修理所有</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //武将/士兵穿上装备 generalId包括武将和士兵
    /**
     * 背包中删除 
     * 武将/士兵中添加
     * @param personGoodId
     * @param  
     */
    async wearingEquipLogic(userId:number,personGoodId: number, generalId: number) {
        let personGood=await this.personGoodsEntity.findOne({where:{id:personGoodId},relations:['good']})
        if(personGood){
            let type=personGood.good.subType
            if(personGood.good.type==2||personGood.good.type==4)type=30
            let generalEquip=new GeneralEquip();
            generalEquip.equipmentId=personGood.good.id;
            //根据物品分类判断是武将还是士兵
            [2,3].includes(personGood.good.type)&&(generalEquip.generalId=generalId);
            [4,5].includes(personGood.good.type)&&(generalEquip.soldierId=generalId);
            personGood.good.type===6&&(generalEquip.mountId=generalId);
            generalEquip.type=type;
            personGood.attack&&(generalEquip.attack=personGood.attack)
            personGood.defense&&(generalEquip.defense=personGood.defense)
            personGood.hp&&(generalEquip.hp=personGood.hp)
            personGood.durability&&(generalEquip.durability=personGood.durability)
            personGood.isBind&&(generalEquip.isBind=personGood.isBind);
            await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
                let equipEntity = await transactionalEntityManager.findOne(GeneralEquip, { where: { generalId,type } });
                if (equipEntity) {
                    await transactionalEntityManager.delete(GeneralEquip, { id: equipEntity.id });
                    let personGoods = await transactionalEntityManager.findOne(PersonGoodsEntity, { where: { userId,good: {id:equipEntity.equipmentId} } });
                    if (personGoods) {
                        await transactionalEntityManager.update(PersonGoodsEntity, { id: personGoods.id }, { count: () => 'count+1' });  
                    }else{
                        await transactionalEntityManager.insert(PersonGoodsEntity, { userId,good:{id:equipEntity.equipmentId}, count: 1 });
                    }
                }
                await transactionalEntityManager.insert(GeneralEquip, generalEquip);
                if(personGood.count==1){
                    await transactionalEntityManager.delete(PersonGoodsEntity, { id: personGoodId }); 
                }else{
                    await transactionalEntityManager.update(PersonGoodsEntity, { id: personGoodId }, { count: () => 'count-1' })   ;
                }
            });
            return '装备成功'
        }
        return '装备不存在'
    }
    //计算战力 type:user,general,soldier itemId
    async calculatePower(type: string, itemId: number) {
        
        const soldierFn = async (soldierId) => {
            let attack = 0, defense = 0, hp = 0;
            let soldierInfo=await this.soldierEntity.findOne({where:{id:soldierId},relations:['general']})
            //获取士兵模板
            let soldierTemp = soldierTemplate[soldierInfo.type]
            //根据等级计算攻击防御血量
            attack=(soldierInfo.general.level+soldierInfo.general.soul+soldierInfo.level)*soldierTemp.addattack
            defense=soldierInfo.level*soldierTemp.adddefense
            hp=soldierInfo.level*soldierTemp.addhp+500
            //2、装备攻击/防御/血量
            let {attack1,defense1,hp1}=await addEquip(soldierInfo)
            attack += attack1*10
            defense += defense1*6
            hp += hp1
            //狂一二三四 罩一二三四 万虫
            attack+=soldierInfo.attackAdd
            defense+=soldierInfo.defenseAdd
            hp+=soldierInfo.hpAdd
            //百分比系列
            if(soldierInfo.raguar==100)attack+=attack*1.3
            if(soldierInfo.extremeRaguar==100)attack+=attack*1.3
            if(soldierInfo.longpoRaguar==100)attack+=attack*1.3
            if(soldierInfo.jingang==100)defense+=defense*1.3
            if(soldierInfo.xueyinJingang==100)defense+=defense*1.3
            if(soldierInfo.longpoJingang==100)defense+=defense*1.3
            if(soldierInfo.qiangti1==100)hp+=hp*1.3
            if(soldierInfo.qiangti2==100)hp+=hp*1.3
            if(soldierInfo.qiangti3==100)hp+=hp*1.3
            //开脉
            if(soldierInfo.kaimaiName){
                let kaimai=handelfn.jingmai(soldierInfo.kaimaiName)
                attack+=attack*kaimai.gongji
                defense+=defense*kaimai.fangyu
            }
            //魂魄
            if(soldierInfo.hunpoName){
                let hunpo=handelfn.hunpo(soldierInfo.hunpoName)
                attack+=attack*hunpo.gongji
                defense+=defense*hunpo.fangyu
            }
            //其他
            await this.soldierEntity.update({id:soldierId},{attack,defense,hp})
        }
        const generalFn = async (generalId, roleInfo) => {
            //武将
            let generalInfo = await this.generalEntity.findOne({
                where: { id: generalId },
            });
            let attack = generalInfo.attackAdd, defense = generalInfo.defenseAdd, hp = 2600+generalInfo.hpAdd, countAll = 8;
            //计算攻击  
            //1、基础攻击(角色等级+角色魄力+武将等级+武将魄力)*10
            attack += roleInfo.level + roleInfo.soul + generalInfo.level + generalInfo.soul
            //基础防御 武将等级*40
            defense += (generalInfo.level) * 40
            //基础血量  武将等级*103
            hp += getHpByLevel(generalInfo.level)
            //基础带兵人口  武将等级*3
            countAll += 8 + generalInfo.level * 3
            //2、装备攻击/防御/血量
            let {attack1,defense1,hp1}=await addEquip(generalInfo)
            attack += attack1
            defense += defense1*6
            hp += hp1
            //狂一二三四 罩一二三四 万虫
            // attack+=generalInfo.attackAdd
            // defense+=generalInfo.defenseAdd
            // hp+=generalInfo.hpAdd
            
            //最后统计
            attack*=10
            //百分比系列
            if(generalInfo.raguar==100)attack+=attack*1.3
            if(generalInfo.extremeRaguar==100)attack+=attack*1.3
            if(generalInfo.longpoRaguar==100)attack+=attack*1.3
            if(generalInfo.jingang==100)defense+=defense*1.3
            if(generalInfo.xueyinJingang==100)defense+=defense*1.3
            if(generalInfo.longpoJingang==100)defense+=defense*1.3
            if(generalInfo.qiangti1==100)hp+=hp*1.3
            if(generalInfo.qiangti2==100)hp+=hp*1.3
            if(generalInfo.qiangti3==100)hp+=hp*1.3
            //开脉
            if(generalInfo.kaimaiName){
                let kaimai=handelfn.jingmai(generalInfo.kaimaiName)
                attack+=attack*kaimai.gongji
                defense+=defense*kaimai.fangyu
            }
            //魂魄
            if(generalInfo.hunpoName){
                let hunpo=handelfn.hunpo(generalInfo.hunpoName)
                attack+=attack*hunpo.gongji
                defense+=defense*hunpo.fangyu
            }
            await this.generalEntity.update({ id: generalId }, { attack, defense, hp, countAll })
        }
        //累加装备的攻击/防御/血量 传入装备列表
        const addEquip = async (generalInfo) => {
            let attack1 = 0, defense1 = 0, hp1 = 0;
            for (const equip in generalEquip) {
                let item=generalEquip[equip]
                if(generalInfo[item.id]){
                    let goodInfo=await this.personGoodsEntity.findOne({where:{id:generalInfo[item.id]}})
                    if(goodInfo?.attack)attack1 += goodInfo.attack
                    if(goodInfo?.defense)defense1 += goodInfo.defense 
                    
                }
                
            }
            return { attack1, defense1, hp1 }
        }
        //人物升级更新所有武将
        if (type == 'user') {
            let roleInfo=await this.roleEntity.findOne({where:{id:itemId}})
            let gens = await this.generalEntity.find({ where: { userId: itemId }, select: ['id'] })
            for (let index = 0; index < gens.length; index++) {
                const element = gens[index];
                await generalFn(element.id,roleInfo)
            }
        }
        if (type == 'general') {
            let gens = await this.generalEntity.findOne({ where: { id: itemId } })
            let roleInfo=await this.roleEntity.findOne({where:{id:gens.userId}})
            //武将
            await generalFn(itemId,roleInfo)
            // for (let index = 0; index < gens.soldiers.length; index++) {
            //     const element = gens.soldiers[index];
            //     await soldierFn(element.id)
            // }
        }
        if (type == 'soldier') {
            //士兵
            await soldierFn(itemId)
        }

    }
    //已穿上装备详情页
    async wearingEquip(sid: string, cmd: number, userId: string, { id }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'wearingEquip', title: '装备详情', service: 'general', params: { id } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let goodInfo = await this.personGoodsEntity.findOne({ where: { id },relations:['good'] })
        let content = `${goodInfo.good.name}<br/>
            ${goodInfo.good.desc ? goodInfo.good.desc + '<br/>' : ''}
            类别:${getGoodType(goodInfo.good.type, goodInfo.good.subType)}<br/>
            重量:${goodInfo.good.weight}<br/>
            价格:${goodInfo.good.price}<br/>
            绑定：${goodInfo.good.bind===1?'绑定':'不绑定'}<br/>
            ${goodInfo.attack ? `攻击力：${goodInfo.attack}<br/>` : ''}
            ${goodInfo.defense ? `防御力：${goodInfo.defense}<br/>` : ''}
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //选择物品 装备 药
    /**
     * 主分类和子分类
     */
    async wearEquip(sid: string, cmd: number, userId: string, { generalId, soldierId, typeId, subTypeId, page = 1 }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'wearEquip', title: '选择装备', service: 'general', params: { generalId, soldierId, typeId, subTypeId, page } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let str = '', subTypestr = '';
        let goodType = goodsType.filter(item => item.id == typeId)
        if (goodType.length && goodType[0].child) {
            goodType[0].child.forEach((item1, index1) => {
                subTypestr += `${subTypeId == item1.id ? `${item1.name} ` : `<a href="${this.seturlOther(params, urlObj, '选择装备', 'wearEquip', { generalId, soldierId, typeId, subTypeId: item1.id })}">${item1.name}</a> `}`
            })
            subTypestr += '<br/>'
        }
        //获取物品列表
        let [goodsList, totalItems] = await this.personGoodsEntity.findAndCount({
            where: { userId: Number(userId), count: MoreThan(0), good: { type: typeId, subType: subTypeId } },
            relations: ['good'],
            take: this.size, skip: (page - 1) * this.size
        })
        let contentStr = '';
        let routerList = JSON.parse(await this.redis.hget('user' + userId, 'routerList'))
        let routerInfo = routerList[routerList.length - 2]
        goodsList.forEach((item, index) => {
            //使用完物品返回上级路由
            contentStr += `<a href="${this.seturlOther(params, urlObj, routerInfo.title, routerInfo.name, { ...routerInfo.params, personGoodId: item.id })}">${index + 1}.${item.good.name}x${item.count}</a></br>`
        })
        //分页
        let pageStr = this.setpage(params, urlObj, page, totalItems, { generalId, soldierId, type: typeId, subType: subTypeId, page }, 'wearEquip', '选择装备')
        let content = `${subTypestr}${contentStr}${pageStr}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //武将/士兵使用物品 默认进来是全部
    async useGoods(sid: string, cmd: number, userId: string, { page = 1, generalId = 0, soldierId = 0, type = 8, subType = 0, thingId = 0 }) {
        let msg=''
        if (thingId) {
            let otype='general',oitemId=generalId
            if(soldierId){
                otype='soldier'
                oitemId=soldierId
            }
            msg=await this.goodsService.canUse(otype,oitemId,thingId)
            if(!msg){
                let goodInfo=await this.personGoodsEntity.findOne({where:{id:thingId},relations:['good']})
                if(['魄力丹','高级魄力丹'].includes(goodInfo.good.name)){
                    return await this.useNumPage(sid, cmd, userId, { generalId, soldierId, type, subType, page, thingId })
                }else{
                    //使用物品逻辑 待实现
                    let res=await this.otherService.useGoodsLogic(otype,oitemId,goodInfo)
                    msg=res.msg
                    if(res.code==='ok'){
                        await this.calculatePower(otype,oitemId)
                    }
                }
                
            }
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'useGoods', title: '使用物品', service: 'general', params: { generalId, soldierId, type, subType, page } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let typestr = '', subTypestr = '';//主分类和子分类
        goodsType1.forEach((item, index) => {
            typestr += `${type == item.id ? `${item.name} ` : `<a href="${this.seturlOther(params, urlObj, '选择物品', 'useGoods', { generalId, soldierId, type: item.id })}">${item.name}</a> `}`
            if (type == item.id && item.child) {
                item.child.forEach((item1, index1) => {
                    subTypestr += `${subType == item1.id ? `${item1.name} ` : `<a href="${this.seturlOther(params, urlObj, '选择物品', 'useGoods', { generalId, soldierId, type: item.id, subType: item1.id })}">${item1.name}</a> `}`
                })
                subTypestr += '<br/>'
            }
        })
        typestr += '<br/>'
        //获取物品列表
        let params1:any={};
        type&&(params1.type=type)&&subType&&(params1.subType=subType)
        let [goodsList, totalItems] = await this.personGoodsEntity.findAndCount({
            where: { userId: Number(userId), good: params1 },
            relations: ['good'],
            take: this.size, skip: (page - 1) * this.size
        })
        let contentStr = '';
        goodsList.forEach((item, index) => {
            contentStr += `<a href="${this.seturlOther(params, urlObj, '使用物品', 'useGoods', { generalId, soldierId, type, subType, page, thingId: item.id })}">${index + 1}.${item.good.name}x${item.count}</a></br>`
        })
        //分页
        let pageStr = this.setpage(params, urlObj, page, totalItems, { generalId, soldierId, type, subType, page }, 'useGoods', '使用物品')

        let content = `${msg}${typestr}${contentStr}${subTypestr}${pageStr}<br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //使用数量页面
    async useNumPage(sid: string, cmd: number, userId: string, { generalId, soldierId, type, subType, page, thingId }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'useNumPage', title: '使用数量', service: 'general', params: { generalId, soldierId, type, subType, page, thingId } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `使用物品<br/>
        <form action="${this.seturlOther(params, urlObj,'使用数量', 'useNumPagePost', { thingId })}" method="post">
        请输入你要使用数量：<input name="count12" type="number" value="1" maxlength="10"/><br/>
        <input name="submit" type="submit" title="使用" value="使用"/></form>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content;
    }
    //使用数量页面 提交
    async useNumPagePost(userId:number,thingId:number,count:number, cmd: number,sid: string){
        count=Number(count);
        let msg='';
        let goodInfo=await this.personGoodsEntity.findOne({ 
            where: { id:thingId },
            relations: ['good'],
        })
        if(!isPureNumber(count)||count<1||count>9999||goodInfo.count<count){
            msg='数量不正确<br/>'
        }
        let routerList = JSON.parse(await this.redis.hget('user' + userId, 'routerList'))
        let routerInfo=routerList.pop()
        if(msg){
            msg+=await this[routerInfo.name](sid,cmd,userId,routerInfo.params)
            return msg
        }
        let otype='general',oitemId=routerInfo.params.generalId
        if(routerInfo.params.soldierId){
            otype='soldier'
            oitemId=routerInfo.params.soldierId
        }
        let res=await this.otherService.useGoodsLogic(otype,oitemId,goodInfo,count)
        msg=res.msg
        if(res.code==='ok'){
            routerInfo.params.thingId=null
        }
        await this.redis.hdel('user' + userId, 'token')
        return msg+await this.useGoods(sid,cmd,String(userId),routerInfo.params)

    }
    //中间页 用于处理确定的 传入提示，按钮名称，点击后跳转页
    async middlePageSure(sid: string, cmd: number, userId: string, { msg, btnName,pageName, pageRoute, type }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'middlePageSure', title: '提示', service: 'general', params:{msg, btnName,pageName, pageRoute, type} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `${msg}<br/>
        <a href="${this.seturlOther(params, urlObj, pageName, pageRoute, {type})}">${btnName}</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;     
    }
    //武将详情
    async generalInfo(sid: string, cmd: number, userId: string, { generalId, accelerate='',type='' }) {
        let generalInfo = await this.getGeneralDetail(generalId);
        if(generalInfo.userId!=Number(userId)){
            return await this.otherSeaService.generalInfo(sid,cmd,userId,{generalId})
        }
        let msg = '';
        //加速
        if (accelerate) {
            let userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
            let manorEventInfo = await this.manorEventEntity.findOneBy({ buildId: generalInfo.id, userId: Number(userId), status: 1 })
            let time = new Date(manorEventInfo.completionTime).getTime() - new Date().getTime()
            let needPot = Math.floor(Math.floor(generalInfo.level / 2) * time / 1000)
            if (needPot > userInfo.potential) {
                msg = `加速需要潜能：${needPot},你的潜能不够</br>`
            } else {
                await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
                    await transactionalEntityManager.update(RoleEntity, { id: Number(userId) }, { potential: () => 'potential - ' + needPot });
                    await transactionalEntityManager.update(GeneralEntity, { id: generalId }, { level: () => 'level + ' + 1, upgradeStatus: 1 });
                    await transactionalEntityManager.update(ManorEventEntity, { buildId: generalId, status: 1, userId: Number(userId) }, { status: 2 });
                });
                await this.calculatePower('general',generalId)
                await this.commonService.generalPopulation(generalId)
                generalInfo = await this.getGeneralDetail(generalId)
                userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
                await this.redis.hset('user' + userId, 'userInfo', JSON.stringify(userInfo))
                msg=`消耗潜能：${needPot},加速成功</br>`
            }
        }
        //开脉
        if (type==='kaimai') {
            let userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
            let {notOpen}=jingmaiFn(generalInfo.kaimaiName)
            let goodNum=await this.goodsService.getGoodNum(Number(userId),40)
            if(userInfo.food<notOpen.populationNum||userInfo.potential<notOpen.populationNum||goodNum.count<notOpen.longdanNum){
                msg = `开脉需要粮草x${notOpen.populationNum},潜能x${notOpen.populationNum},龙灵仙凤丹x${notOpen.longdanNum},你的资源不够</br>`
            } else {
                await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
                    await transactionalEntityManager.update(RoleEntity, { id: Number(userId) }, { food: () => 'food -'+ notOpen.populationNum, potential: () => 'potential -'+ notOpen.populationNum });
                    if(goodNum.count==notOpen.longdanNum){
                        await transactionalEntityManager.delete(PersonGoodsEntity, { userId: Number(userId), goodId: 40 });
                    }else{
                        await transactionalEntityManager.update(PersonGoodsEntity, { userId: Number(userId), goodId: 40 }, { count: () => 'count -'+ notOpen.longdanNum });
                    }
                    await transactionalEntityManager.update(GeneralEntity, { id: generalId }, { kaimaiName: notOpen.name });
                    msg=`消耗粮草x${notOpen.populationNum},潜能x${notOpen.populationNum},龙灵仙凤丹x${notOpen.longdanNum},开启${notOpen.name}成功</br>`
                })
            }
        }
        //魂魄
        if(type==='hunpo'){
            let userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
            let {notOpen}=hunpofn(generalInfo.hunpoName)
            let goodNum=await this.goodsService.getGoodNum(Number(userId),notOpen.hunpoId)
            if(
                userInfo.food<notOpen.populationNum||
                userInfo.wood<notOpen.populationNum||
                userInfo.iron<notOpen.populationNum||
                userInfo.stone<notOpen.populationNum||
                userInfo.potential<notOpen.populationNum||
                goodNum.count<notOpen.hunpoNum){
                msg = `打通魂魄[${notOpen.name}]需要粮草、木材、生铁、石料、潜能各x${notOpen.populationNum},魂魄灵珠[${notOpen.name}]x${notOpen.hunpoNum},你的资源不够</br>`
            } else {
                await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
                    await transactionalEntityManager.update(RoleEntity, { id: Number(userId) }, { 
                        food: () => 'food -'+ notOpen.populationNum, 
                        potential: () => 'potential -'+ notOpen.populationNum,
                        wood: () => 'wood -'+ notOpen.populationNum,
                        iron: () => 'iron -'+ notOpen.populationNum,
                        stone: () => 'stone -'+ notOpen.populationNum 
                    });
                    if(goodNum.count==notOpen.hunpoNum){
                        await transactionalEntityManager.delete(PersonGoodsEntity, { userId: Number(userId), goodId: notOpen.hunpoId  });
                    }else{
                        await transactionalEntityManager.update(PersonGoodsEntity, { userId: Number(userId), goodId: notOpen.hunpoId }, { count: () => 'count -'+ notOpen.hunpoNum });
                    }
                    await transactionalEntityManager.update(GeneralEntity, { id: generalId }, { hunpoName: notOpen.name });
                    msg=`消耗粮草、木材、生铁、石料、潜能各x${notOpen.populationNum},魂魄灵珠[${notOpen.name}]x${notOpen.longdanNum},打通魂魄[${notOpen.name}]成功</br>`
                })
            }
        }
        //跳下坐骑
        if(type==='mount'){
            let res=await this.mountEntity.update({generalId},{status:1,generalId:null})
            if(res.affected>0){
                msg=`跳下坐骑成功</br>`
            }else{
                msg=`跳下坐骑失败</br>`
            }
        }
        //撤销、设置近身
        if(type==='delnear'){
            let res=await this.generalEntity.update({id:generalId},{near:0})
            if(res.affected>0){
                msg=`撤销近身成功</br>`
            }else{
                msg=`撤销近身失败</br>`
            }
            generalInfo = await this.getGeneralDetail(generalId);
        }
        //撤销、设置近身
        if(type==='setnear'){
            //near 大于0
            let generalList = await this.getGeneralList(userId)
            generalList = generalList.filter(item => item.near)
            if (generalList.length >= this.nearBygeneralNum){
                msg = `近身武将已满，不能再设置近身了</br>`
            }else{
                let res = await this.generalEntity.save({ id: generalId, near: generalList.length + 1 })
                if (res) {
                    msg = `设置近身成功</br>`
                } else {
                    msg = `设置近身失败</br>`
                }
            }
            
            generalInfo = await this.getGeneralDetail(generalId);
        }
        //取消升级
        if(type==='cancelUpgrade'){
            let manorEventInfo = await this.manorEventEntity.findOneBy({ buildId: generalId, userId: Number(userId), status: 1,type:4 })
            if(manorEventInfo){
                // let num=manorEventInfo.num2-manorEventInfo.num1
                // let source=generalTemplate[generalInfo.type]
                // msg=`取消升级成功，收回资源：粮草+${num*source.recruitFood},银两+${num*source.recruitGold}<br/>`
                // await this.roleEntity.update(userId, {
                //     food: () => `food + ${num*source.recruitFood}`, // 添加空格使SQL更清晰
                //     gold: () => `gold + ${num*source.recruitGold}`
                // })
                //暂时不回收资源
                await this.manorEventEntity.update(manorEventInfo.id, { status: 2 })
                await this.generalEntity.update(generalId, { upgradeStatus: 1 })
                generalInfo.upgradeStatus =1
                msg=`取消升级成功</br>`
                await this.tasksService.clearInterval('upgrade' + manorEventInfo.id)
                // this.eventEmitter.emit('writeLogs', { userId, name: '取消升级士兵:' + soldierInfo.name, gold: num*source.recruitGold, food: num*source.recruitFood })
            }else{
                msg='升级已完成,无法取消<br/>'
            }
        }



        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'generalInfo', title: generalInfo.name, service: 'general', params: { generalId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        //所带兵种
        let str2 = '';
        let ids=generalInfo.soldiers.map(item => item.id)
        let soldierEvent=await this.manorEventEntity.findBy({buildId:In(ids),userId:Number(userId),status:1,type:2})
        generalInfo.soldiers.forEach((item, index) => {
            str2 += `<a href="${this.seturlOther(params, urlObj, item.name, 'soldierInfo', { generalId, soldierId: item.id })}">${item.name}</a>${
                soldierEvent.some(item1=>item1.buildId==item.id)?
                `[招募中]`:`[<a href="${this.seturlOther(params, urlObj, '招募', 'soldierRecruit', { generalId, soldierId: item.id })}">招募</a>]`
            } `
        })
        let equipNameStr=''
        for (const key in generalEquip) {
            let item=generalEquip[key]
            if(generalInfo[item.id]&&key!='2'){
                equipNameStr+=generalInfo[item.title]+','
            }
        }
        let fighting = ''
        if (generalInfo.fightStatus == 1) {
            let fightInfo = await this.fightEntity.findOne({
                where: [
                    { status: 1, fightGenerals: { attackGeneralId: String(generalInfo.id) } }
                ], select: ['id']
            })
            if (fightInfo) {
                fighting += `${generalInfo.name}正在战斗中...<a href="${this.seturlOther(params, urlObj, '战场', 'fighting', { fightingId: fightInfo.id }, 'fight')}">查看战况</a></br>`
            }
        }
        let upgradeStr = ''
        let manorEventInfo = await this.manorEventEntity.findOneBy({ buildId: generalInfo.id, userId: Number(userId), status: 1,type:4 })
        if(manorEventInfo){
            let time = formatDuring(new Date(manorEventInfo.completionTime).getTime() - new Date().getTime())
            upgradeStr = `正在升级:剩余${time}...<a href="${this.seturlOther(params, urlObj, generalInfo.name, 'generalInfo', { generalId })}">刷新</a>
            <a href="${this.seturlOther(params, urlObj, generalInfo.name, 'generalInfo', { accelerate: true, generalId })}">加速</a>
            <a href="${this.seturlOther(params, urlObj, '确认', 'confirmPage', { msg:"取消事件只能收回部分未用完的资源，确定要取消这个事件吗？",pageName:'generalInfo',serviceName:'general', other:{generalId,type:'cancelUpgrade'} })}">取消</a>`
        }else{
            upgradeStr = `<a href="${this.seturlOther(params, urlObj, '升级', 'upgradePage', { attrItem: 'gen&' + generalInfo.id, hidden: true }, 'general')}">升级</a>`
        }
        let blessCount = await this.blessEntity.count({ where: { generalId, status: 1 } })
        let {opened,notOpen}=jingmaiFn(generalInfo.kaimaiName)
        let {opened : openedHunpo,notOpen:notOpenHunpo}=hunpofn(generalInfo.hunpoName)
        let mountInfo = await this.mountEntity.findOne({ where: { generalId }})
        let content = `${msg}${generalInfo.name}${generalInfo.near ? '(近身)' : ''}${generalAttrHandel('general',generalInfo)}<br/>
        等级:${generalInfo.level} [${upgradeStr}]<br/>
        魄力:${generalInfo.soul}<br/>
        生命:${generalInfo.hpNow}/${generalInfo.hp}<br/>
        攻击力:${generalInfo.attack}<br/>
        防御力:${generalInfo.defense}<br/>
        已通经脉:${opened}[<a href="${this.seturlOther(params, urlObj, '提示', 'middlePageSure', {msg:notOpen.value, btnName:'确认开通',pageRoute: 'generalInfo', pageName: generalInfo.name,type:'kaimai'  })}">打通${notOpen.name}</a>]<br/>
        五行魂魄:${openedHunpo}[<a href="${this.seturlOther(params, urlObj, '提示', 'middlePageSure', {msg:notOpenHunpo.value, btnName:'确认开通',pageRoute: 'generalInfo', pageName: generalInfo.name,type:'hunpo'  })}">魂魄${notOpenHunpo.name}</a>]<br/>
        兵器:${generalInfo.bingqiName||''}
        防具:${equipNameStr}<br/>
        可带兵种:${str2}<br/>
        坐骑:${mountInfo?
                `<a href="${this.seturlOther(params, urlObj, '坐骑详情', 'mountDetail', {mountId:mountInfo.id},'generalother')}">${mountConfig[mountInfo.mountType].name}</a> <a href="${this.seturlOther(params, urlObj, generalInfo.name, 'generalInfo', {generalId,type:'mount'})}">跳下</a>`
                :`<a href="${this.seturlOther(params, urlObj, '选择坐骑', 'checkMount', {type:1,itemId:generalId},'generalother')}">骑上</a>`
            }<br/>
        带领军队:<a href="${this.seturlOther(params, urlObj, generalInfo.name+'部', 'armyGeneral', {generalId:generalInfo.id})}">${generalInfo.name}部</a><br/>
        带兵数量:${generalInfo.population}<br/>
        带兵人口:${generalInfo.count}/${generalInfo.countAll}<br/>
        <a href="${this.seturlOther(params, urlObj, generalInfo.name+'祝福', 'blessingList', { type:'general',id:generalId },'generalother')}">祝福状态(${blessCount})</a><br/>
        ${fighting||`<a href="${this.seturlOther(params, urlObj, '使用物品', 'useGoods', { generalId })}">使用物品</a><br/>`}
        <a href="${this.seturlOther(params, urlObj, '技能列表', 'skillList', { generalId})}">技能</a>&nbsp;<a href="${this.seturlOther(params, urlObj, '技能列表', 'skillList', { generalId, skillType: 2 })}">必杀</a>&nbsp;
        <a href="${this.seturlOther(params, urlObj, '武将装备', 'generalEquipDetail', { generalId })}">装备</a>(<a href="${this.seturlOther(params, urlObj, '装备套装介绍', 'suitInfo')}">?</a>)<br/>
        <a href="${this.seturlOther(params, urlObj, generalInfo.name, 'generalInfo', { generalId, type: generalInfo.near?'delnear':'setnear' })}">${generalInfo.near ? '撤销' : '设为'}近身武将</a><br/>
        <a href="${this.seturlOther(params, urlObj, '学习原始兵种', 'learnSoldier', { generalId },'generalother')}">学习原始兵种</a><br/>
        <a href="${this.seturlOther(params, urlObj, '设置顺序', 'adjustGeneral', { generalId },'generalother')}">设置顺序</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //计算武将属性
    async computedGeneral(generalInfo: GeneralEntity, roleInfo: RoleEntity) {
        let initGj = 0, initFY = 0, xueliang = getHpByLevel(generalInfo.level), renkou = 8 + generalInfo.level * 3;
        //角色等级 每级+10攻击
        initGj += roleInfo.level * 10
        //角色魄力 每级+10攻击
        initGj += roleInfo.soul * 10
        //武将等级 每级+10攻击
        initGj += generalInfo.level * 10
        initFY += generalInfo.level * 40
        xueliang += generalInfo.level * 103
        //武将魄力 每级+10攻击
        initGj += generalInfo.soul * 10
        return { initGj, initFY, xueliang, renkou }
    }
    //士兵详情
    async soldierInfo(sid: string, cmd: number, userId: string, { soldierId,accelerate='',manorEventId='',type='' }) {
        let soldierInfo = await this.getSoldierDetail(soldierId)
        let msg = '';
        //加速
        if (accelerate) {
            let userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
            let manorEventInfo = await this.manorEventEntity.findOneBy({ buildId: soldierInfo.id, userId: Number(userId), status: 1 })
            if(!manorEventInfo){
                msg='升级已完成,无法加速<br/>'
            }else{
                let time = new Date(manorEventInfo.completionTime).getTime() - new Date().getTime()
                let needPot = Math.floor(Math.floor(soldierInfo.level / 2) * time / 1000)
                if (needPot > userInfo.potential) {
                    msg = `加速需要潜能：${needPot},你的潜能不够</br>`
                } else {
                    await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
                        await transactionalEntityManager.update(RoleEntity, { id: Number(userId) }, { potential: () => 'potential - ' + needPot });
                        await transactionalEntityManager.update(SoldierEntity, { id: soldierId }, { level: () => 'level + ' + 1, upgradeStatus: 1 });
                        await transactionalEntityManager.update(ManorEventEntity, { buildId: soldierId, status: 1, userId: Number(userId) }, { status: 2 });
                    });
                    await this.calculatePower('soldier',soldierId)
                    soldierInfo = await this.getSoldierDetail(soldierId)
                    userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
                    await this.redis.hset('user' + userId, 'userInfo', JSON.stringify(userInfo))
                    msg=`消耗潜能：${needPot},加速成功</br>`
                }
            }
        }
        //取消招募
        if(manorEventId){
            let manorEventId1=Number(manorEventId)
            let manorEventInfo = await this.manorEventEntity.findOneBy({ id: manorEventId1 })
            if(manorEventInfo&&manorEventInfo.status==1){
                let num=manorEventInfo.num2-manorEventInfo.num1
                let source=soldierTemplate[soldierInfo.type]
                msg=`取消招募成功，收回资源：粮草+${num*source.recruitFood},银两+${num*source.recruitGold}<br/>`
                await this.roleEntity.update(userId, {
                    food: () => `food + ${num*source.recruitFood}`, // 添加空格使SQL更清晰
                    gold: () => `gold + ${num*source.recruitGold}`
                })
                await this.manorEventEntity.update(manorEventId1, { status: 2 })
                await this.tasksService.clearInterval('recruit' + manorEventId1)
                this.eventEmitter.emit('writeLogs', { userId, name: '取消招募士兵:' + soldierInfo.name, gold: num*source.recruitGold, food: num*source.recruitFood })
            }else{
                msg='招募已完成,无法取消<br/>'
            }
            
        }
        //开脉
        if (type==='kaimai') {
            let userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
            let {notOpen}=jingmaiFn(soldierInfo.kaimaiName)
            let goodNum=await this.goodsService.getGoodNum(Number(userId),40)
            if(userInfo.food<notOpen.populationNum||userInfo.potential<notOpen.populationNum||goodNum.count<notOpen.longdanNum){
                msg = `开脉需要粮草:${notOpen.populationNum},潜能:${notOpen.populationNum},龙灵仙凤丹:${notOpen.longdanNum},你的资源不够</br>`
            } else {
                await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
                    await transactionalEntityManager.update(RoleEntity, { id: Number(userId) }, { food: () => 'food -'+ notOpen.populationNum, potential: () => 'potential -'+ notOpen.populationNum });
                    if(goodNum.count==notOpen.longdanNum){
                        await transactionalEntityManager.delete(PersonGoodsEntity, { userId: Number(userId), goodId: 40 });
                    }else{
                        await transactionalEntityManager.update(PersonGoodsEntity, { userId: Number(userId), goodId: 40 }, { count: () => 'count -'+ notOpen.longdanNum });
                    }
                    await transactionalEntityManager.update(SoldierEntity, { id: soldierId }, { kaimaiName: notOpen.name });
                    msg=`消耗粮草:${notOpen.populationNum},潜能:${notOpen.populationNum},龙灵仙凤丹:${notOpen.longdanNum},开启${notOpen.name}成功</br>`
                })
            }
        }
        //魂魄
        if(type==='hunpo'){
            let userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
            let {notOpen}=jingmaiFn(soldierInfo.hunpoName)
            let goodNum=await this.goodsService.getGoodNum(Number(userId),notOpen.hunpoId)
            if(
                userInfo.food<notOpen.populationNum||
                userInfo.wood<notOpen.populationNum||
                userInfo.iron<notOpen.populationNum||
                userInfo.stone<notOpen.populationNum||
                userInfo.potential<notOpen.populationNum||
                goodNum.count<notOpen.hunpoNum){
                msg = `打通魂魄[${notOpen.name}]需要粮草、木材、生铁、石料、潜能各x${notOpen.populationNum},魂魄灵珠[${notOpen.name}]x${notOpen.hunpoNum},你的资源不够</br>`
            } else {
                await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
                    await transactionalEntityManager.update(RoleEntity, { id: Number(userId) }, { 
                        food: () => 'food -'+ notOpen.populationNum, 
                        potential: () => 'potential -'+ notOpen.populationNum,
                        wood: () => 'wood -'+ notOpen.populationNum,
                        iron: () => 'iron -'+ notOpen.populationNum,
                        stone: () => 'stone -'+ notOpen.populationNum 
                    });
                    if(goodNum.count==notOpen.hunpoNum){
                        await transactionalEntityManager.delete(PersonGoodsEntity, { userId: Number(userId), goodId: notOpen.hunpoId  });
                    }else{
                        await transactionalEntityManager.update(PersonGoodsEntity, { userId: Number(userId), goodId: notOpen.hunpoId }, { count: () => 'count -'+ notOpen.hunpoNum });
                    }
                    await transactionalEntityManager.update(SoldierEntity, { id: soldierId }, { hunpoName: notOpen.name });
                    msg=`消耗粮草、木材、生铁、石料、潜能各x${notOpen.populationNum},魂魄灵珠[${notOpen.name}]x${notOpen.longdanNum},打通魂魄[${notOpen.name}]成功</br>`
                })
            }
        }
        //跳下坐骑
        if(type==='mount'){
            let res=await this.mountEntity.update({soldierId},{status:1,soldierId:null})
            if(res.affected>0){
                msg=`跳下坐骑成功</br>`
            }else{
                msg=`跳下坐骑失败</br>`
            }
        }
        //取消升级
        if(type==='cancelUpgrade'){
            let manorEventInfo = await this.manorEventEntity.findOneBy({ buildId: soldierId, userId: Number(userId), status: 1,type:4 })
            if(manorEventInfo){
                // let num=manorEventInfo.num2-manorEventInfo.num1
                // let source=soldierTemplate[soldierInfo.type]
                // msg=`取消升级成功，收回资源：粮草+${num*source.recruitFood},银两+${num*source.recruitGold}<br/>`
                // await this.roleEntity.update(userId, {
                //     food: () => `food + ${num*source.recruitFood}`, // 添加空格使SQL更清晰
                //     gold: () => `gold + ${num*source.recruitGold}`
                // })
                //暂时不回收资源
                await this.manorEventEntity.update(manorEventInfo.id, { status: 2 })
                await this.soldierEntity.update(soldierId, { upgradeStatus: 1 })
                soldierInfo.upgradeStatus =1
                msg=`取消升级成功</br>`
                await this.tasksService.clearInterval('upgrade' + manorEventInfo.id)
                // this.eventEmitter.emit('writeLogs', { userId, name: '取消升级士兵:' + soldierInfo.name, gold: num*source.recruitGold, food: num*source.recruitFood })
            }else{
                msg='升级已完成,无法取消<br/>'
            }
        }



        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'soldierInfo', title: soldierInfo.name, service: 'general', params: { soldierId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        //装备
        let equipNameStr=''
        for (const key in generalEquip) {
            let item=generalEquip[key]
            if(soldierInfo[item.id]&&key!='2'){
                equipNameStr+=soldierInfo[item.title]+','
            }
        }
        let upgradeStr = ''
        if (soldierInfo.upgradeStatus == 1) {
            upgradeStr = `<a href="${this.seturlOther(params, urlObj, '升级', 'upgradePage', { attrItem: 'sol&' + soldierInfo.id, hidden: true }, 'general')}">升级</a>`
        } else {
            let manorEventInfo = await this.manorEventEntity.findOneBy({ buildId: soldierInfo.id, userId: Number(userId), status: 1,type:4 })
            if(manorEventInfo){
                let time = formatDuring(new Date(manorEventInfo.completionTime).getTime() - new Date().getTime())
                upgradeStr = `正在升级:剩余${time}...<a href="${this.seturlOther(params, urlObj, soldierInfo.name, 'soldierInfo', { soldierId })}">刷新</a>
                <a href="${this.seturlOther(params, urlObj, soldierInfo.name, 'soldierInfo', { accelerate: true, soldierId })}">加速</a>
                <a href="${this.seturlOther(params, urlObj, '确认', 'confirmPage', { msg:"取消事件只能收回部分未用完的资源，确定要取消这个事件吗？",pageName:'soldierInfo',serviceName:'general', other:{soldierId,type:'cancelUpgrade'} })}">取消</a>`
            }else{
                upgradeStr='未知错误'
            }
            
        }
        //其他属性处理
        const otherMsgFn=()=>{
            let otherMsg = '';
            if(soldierInfo.raguar){
                if(soldierInfo.extremeRaguar){
                    otherMsg=soldierInfo.extremeRaguar>=100?'[极度狂暴]':`[极度狂暴:${soldierInfo.extremeRaguar}/100]`;
                }else{
                    otherMsg=soldierInfo.raguar>=100?'[狂暴]':`[狂暴:${soldierInfo.raguar}/100]`;
                }
            }
            if(soldierInfo.jingang){
                otherMsg+=soldierInfo.jingang>=100?'[金刚]':`[金刚:${soldierInfo.jingang}/100]`;
            }
            return otherMsg;
        }
        let str1=''
        let soldierEvent=await this.manorEventEntity.findOneBy({buildId:soldierInfo.id,userId:Number(userId),status:1,type:2})
        if(soldierEvent){
            str1=`目前正在招募${soldierEvent.num1}/${soldierEvent.num2}名步兵[剩余${formatDuring(new Date(soldierEvent.completionTime).getTime() - new Date().getTime())}/${soldierTemplate[soldierInfo.type].recruitTime}秒...
            <a href="${this.seturlOther(params, urlObj, soldierInfo.name, 'soldierInfo', { soldierId })}">刷新</a>
            <a href="${this.seturlOther(params, urlObj, '确认', 'confirmPage', { msg:"取消事件只能收回部分未用完的资源，确定要取消这个事件吗？",pageName:'soldierInfo',serviceName:'general', other:{soldierId,manorEventId:soldierEvent.id} })}">取消</a>]<br/>`
        }
        let {opened,notOpen}=jingmaiFn(soldierInfo.kaimaiName)
        let {opened : openedHunpo,notOpen:notOpenHunpo}=hunpofn(soldierInfo.hunpoName)
        let blessCount = await this.blessEntity.count({ where: { soldierId, status: 1 } })
        let mountInfo = await this.mountEntity.findOne({ where: { soldierId }})
        let content = `${msg}${soldierInfo.name}${otherMsgFn()}<br/>
        ${soldierTemplate[soldierInfo.type].desc}<br/>
        兵种等级:${soldierInfo.level} [${upgradeStr}]<br/>
        所属武将:<a href="${this.seturlOther(params, urlObj, soldierInfo.general.name, 'generalInfo', { generalId: soldierInfo.general.id })}">${soldierInfo.general.name}</a><br/>
        攻击力:${soldierInfo.attack}<br/>
        五行攻击:无<br/>
        防御力:${soldierInfo.defense}<br/>
        五行抗性:无<br/>
        兵器:${soldierInfo?.bingqiName||''}<br/>
        防具:${equipNameStr}<br/>
        最大生命:${soldierInfo.hp}<br/>
        占用人口:${soldierInfo.population}<br/>
        士兵数量:${soldierInfo.count}${!str1 ? `[<a href="${this.seturlOther(params, urlObj, '招募', 'soldierRecruit', { soldierId,hidden:true })}">招募</a>]` : ''}<br/>
        ${str1}
        已通经脉:${opened}[<a href="${this.seturlOther(params, urlObj, '提示', 'middlePageSure', {msg:notOpen.value, btnName:'确认开通',pageRoute: 'soldierInfo', pageName: soldierInfo.name,type:'kaimai'  })}">打通${notOpen.name}</a>]<br/>
        五行魂魄:${openedHunpo}[<a href="${this.seturlOther(params, urlObj, '提示', 'middlePageSure', {msg:notOpenHunpo.value, btnName:'确认开通',pageRoute: 'soldierInfo', pageName: soldierInfo.name,type:'hunpo'  })}">魂魄${notOpenHunpo.name}</a>]<br/>
        坐骑:${mountInfo?
                `<a href="${this.seturlOther(params, urlObj, '坐骑详情', 'mountDetail', {mountId:mountInfo.id},'generalother')}">${mountConfig[mountInfo.mountType].name}</a> <a href="${this.seturlOther(params, urlObj, soldierInfo.name, 'soldierInfo', {soldierId,type:'mount'})}">跳下</a>`
                :`<a href="${this.seturlOther(params, urlObj, '选择坐骑', 'checkMount', {type:2,itemId:soldierId},'generalother')}">骑上</a>`
            }<br/>
        招募代价: 粮草x20 银两x20 时间x20秒<br/>
        <a href="${this.seturlOther(params, urlObj, soldierInfo.name+'祝福', 'blessingList', { type:'soldier',id:soldierId },'generalother')}">祝福状态(${blessCount})</a><br/>
        <a href="${this.seturlOther(params, urlObj, '技能列表', 'skillList', { soldierId})}">技能</a>&nbsp;<a href="${this.seturlOther(params, urlObj, soldierInfo.name+'的装备', 'soldierEquipDetail', { soldierId })}">装备</a>(<a href="${this.seturlOther(params, urlObj, '装备套装介绍', 'suitInfo')}">?</a>)<br/>
        <a href="${this.seturlOther(params, urlObj, '使用物品', 'useGoods', { soldierId })}">使用物品</a><br/>
        <a href="${this.seturlOther(params, urlObj, '补充士兵', 'buchongSoldier', { soldierId,type:'buchong' },'generalother')}">补充士兵</a><br/>
        <a href="${this.seturlOther(params, urlObj, '调离士兵', 'buchongSoldier', { soldierId,type:'diaoli' },'generalother')}">调离士兵</a><br/>
        <a href="${this.seturlOther(params, urlObj, '遗忘原始兵种', 'forgetSoldier', { soldierId},'generalother')}">遗忘原始兵种</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //人物 武将、士兵升级页面
    async upgradePage(sid: string, cmd: number, userId: string, { attrItem }) {
        let [itemType, itemId] = attrItem.split('&')
        let hidden = true
        let info: any
        let str = ''
        let lvl: number
        if (itemType == 'role') {
            info = JSON.parse(await this.redis.hget('user' + itemId, 'userInfo'))
            lvl = info.level
        }
        if (itemType == 'gen') {
            let info = await this.generalEntity.findOne({ where: { id: Number(itemId) }, select: ['level'] })
            lvl = info.level
        }
        if (itemType == 'sol') {
            let info = await this.soldierEntity.findOne({ where: { id: Number(itemId) }, select: ['level'] })
            lvl = info.level
        }
        let { resource, time } = roleUpgrade(lvl)
        str = `升级需要 粮草x${resource} 木材x${resource} 石料x${resource} 生铁x${resource} 时间x${formatDuring(time * 1000)}`
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'upgradePage', title: '升级', service: 'general', params: { attrItem, hidden } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `${str}。。<br/>
            <form action="${this.seturlOther(params, urlObj, '招募', 'gcmd', { thingId: attrItem })}" method="post"><input name="count3" type="hidden" value="1"><input type="submit" value="确定升级" name="submit"></form>
            <a href="${this.seturlOther(params, urlObj, '升级', 'upgradeMorePage', { attrItem, hidden })}">一次升多级</a><br/>
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content;
    }
    //人物 武将、士兵升级页面-多级
    async upgradeMorePage(sid: string, cmd: number, userId: string, { attrItem }) {
        let [itemType, itemId] = attrItem.split('&')
        let hidden = true
        let userInfo = await this.roleEntity.findOneBy({ id: Number(userId) })
        let lvl=userInfo.level
        if(itemType=='gen'){
            let info = await this.generalEntity.findOne({ where: { id: Number(itemId) }, select: ['level'] })
            lvl = info.level
        }
        if(itemType=='sol'){
            let info = await this.soldierEntity.findOne({ where: { id: Number(itemId) }, select: ['level'] })
            lvl = info.level
        }
        let str = ''
        let arr = [userInfo.food, userInfo.wood, userInfo.stone, userInfo.iron]
        let minItem = Math.min(...arr)
        let { resource, end, pot } = checkMaxLevel(lvl, minItem, userInfo.potential)
        str = `你最多能升级${end-lvl}级，需要花费 粮草x${resource} 木材x${resource} 石料x${resource} 生铁x${resource} 潜能x${pot}`
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'upgradePage', title: '升级', service: 'general', params: { attrItem, hidden } } }
        let goodNum=await this.goodsService.getGoodNumByName(Number(userId),'升级牌')
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `<p>当需要一次性升级很多级时，每次使用需要消耗升级牌x1。<br/>
            你有升级牌x${goodNum}，能使用一次升多级功能<br/>
            ${str}<br/>
            <form action="${this.seturlOther(params, urlObj, '升级', 'levelUpPost', { thingId: attrItem })}" method="post">
            请输入升级级数：<input name="count3" type="number" maxlength="5"/><br/>
            <input type="submit" title="确定升级" value="确定升级"/></form><br/>
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        await this.redis.hset('user' + userId, 'token', userId)
        return content;
    }
    //装备套装介绍
    async suitInfo(sid: string, cmd: number, userId: string) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'suitInfo', title: '装备套装', service: 'general', params: {} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `套装介绍<br/>
            当武将或者士兵穿戴上部分特殊套装的时候可以获得一些额外的套装加成特效:<br/>
            ----<br/>
            星光套装<br/>
            穿戴3件:增加150点生命,5点攻击,5点防御<br/>
            穿戴5件:增加250点生命,15点攻击,15点防御<br/>
            穿戴8件:增加800点生命,40点攻击,40点防御<br/>
            穿戴9件:增加920点生命,46点攻击,46点防御<br/>
            ----<br/>
            仲夏套装<br/>
            穿戴3件:增加160点生命,6点攻击,6点防御<br/>
            穿戴5件:增加280点生命,18点攻击,18点防御<br/>
            穿戴8件:增加900点生命,45点攻击,45点防御<br/>
            ----<br/>
            赤霄套装<br/>
            穿戴3件:增加200点生命,8点攻击,8点防御<br/>
            穿戴5件:增加350点生命,25点攻击,25点防御<br/>
            穿戴8件:增加1000点生命,60点攻击,60点防御<br/>
            穿戴9件:增加1120点生命,64点攻击,64点防御<br/>
            ----<br/>
            天晶套装<br/>
            穿戴3件:增加220点生命,10点攻击<br/>
            穿戴5件:增加380点生命,28点攻击<br/>
            穿戴8件:增加1200点生命,65点攻击<br/>
            穿戴9件:增加1320点生命,68点攻击<br/>
            ----<br/>
            元神套装(奥运套装)<br/>
            穿戴3件:增加260点生命,14点攻击,14点防御<br/>
            穿戴5件:增加440点生命,44点攻击,44点防御<br/>
            穿戴8件:增加1600点生命,75点攻击,75点防御<br/>
            穿戴9件:增加1720点生命,85点攻击,85点防御<br/>
            ----<br/>${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //技能列表 skillType是否必杀技能
    async skillList(sid: string, cmd: number, userId: string, { generalId, soldierId, skillType=1 }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'skillList', title: '技能列表', service: 'general', params: { generalId, soldierId } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let info = generalId ? await this.getGeneralDetail(generalId) : await this.getSoldierDetail(soldierId)
        let str = `${info.name}的技能：<br/>`
        info.skills.filter(item => item.type == skillType).forEach((item, index) => {
            str += `${index + 1}.${item.isDefault ? '*' : ''}<a href="${this.seturlOther(params, urlObj, '技能详情', 'skillInfo', { skillId: item.id })}">${item.name}</a><br/>`
        })
        let content = `${str}${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //技能详情 setDefault 1设置 2取消
    async skillInfo(sid: string, cmd: number, userId: string, { skillId, setDefault }) {
        //设置默认 先把其他默认取消
        let skillInfo = await this.skillEntity.findOne({
            where: { id: Number(skillId) },
            relations: ['general', 'soldier']
        });
        let isGeneral = skillInfo?.general?.id ? true : false
        if (setDefault) {
            if (setDefault == 2) {
                await this.skillEntity.update({ id: Number(skillId) }, { isDefault: 0 })
            } else {

                let generalOrSoldier;//
                if (isGeneral) generalOrSoldier = await this.getGeneralDetail(skillInfo.general.id)
                if (!isGeneral) generalOrSoldier = await this.getSoldierDetail(skillInfo.soldier.id)
                let arr = generalOrSoldier.skills.map(item => ({ id: item.id, isDefault: skillId == item.id ? 1 : 0 }))
                await this.skillEntity.save(arr)
            }
            skillInfo.isDefault = setDefault == 2 ? 0 : 1

        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'skillInfo', title: '技能详情', service: 'general', params: { skillId } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = ` ${skillInfo.name}<br/>
        伤害系数:${skillInfo.damage}<br/>
        攻击距离:${skillInfo.distance}<br/>
        伤害数量:${skillInfo.damageNum}<br/>
        兵器类型:${weaponType[skillInfo.weaponType]}<br/>
        <a href="${this.seturlOther(params, urlObj, '技能详情', 'skillInfo', { skillId, setDefault: skillInfo.isDefault == 1 ? 2 : 1 })}">${skillInfo.isDefault ? '取消' : '设为'}默认使用技能</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //招募士兵
    async soldierRecruit(sid: string, cmd: number, userId: string, { soldierId }) {
        let soldierInfo = await this.getSoldierDetail(soldierId);
        let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: Number(userId) })
        let generalInfo = await this.getGeneralDetail(soldierInfo.general.id);
        let generalList = await this.getGeneralList(userId);
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'home', title: '游戏首页', service: 'gcmd', params: {} } }
        let soldierAllNumRole = 0;
        generalList.forEach(item => {
            soldierAllNumRole += item.count
        });
        let recruitNum = Math.floor(Math.min(userInfo.count - userInfo.currentCount, generalInfo.countAll - generalInfo.count) / soldierInfo.population)
        recruitNum = recruitNum > 0 ? recruitNum : 0
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `<p>招募一名士兵需要 粮草x${soldierTemplate[soldierInfo.type].recruitFood} 银两x${soldierTemplate[soldierInfo.type].recruitGold} 时间x${soldierTemplate[soldierInfo.type].recruitTime}秒<br/>
        你现在有粮草${userInfo.food}，银两${userInfo.gold}<br/>
        ${generalInfo.name}带领的士兵人口：${generalInfo.count}/${generalInfo.countAll}<br/>
        你的当前人口：${userInfo.currentCount}/${userInfo.count}<br/>
        请输入你要招募士兵的数量(你目前还可以招募${recruitNum}名士兵)：<br/>
        </p>
        <form action="${this.seturlOther(params, urlObj, '招募', 'recruitSoldiersPost', { thingId: soldierId })}" method="post">
        招募数量:<input name="count2" type="number" value="${recruitNum}"/><br/>
        <input name="submit" type="submit" title="招募士兵" value="招募士兵"/></form><br/>
        ${backRouter}`;
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj));
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content;
    }
    //升级提交  attrItem:类型&id
    async levelUpPost(userId: number, attrItem: string, count: number, cmd: number, sid: string) {
        let [itemType, itemId] = attrItem.split('&')
        count = Number(count);
        let errMsg = '';
        if(!count||count<1){
            errMsg='升级级数不正确'
        }
        let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: userId })
        let generalInfo:GeneralEntity,soldierInfo:SoldierEntity;
        let resource=0,time=0;
        if(itemType=='role'){
            if (userInfo.level +count>100) {
                errMsg = '目前最高等级为100级'
            }else{
                let obj=roleUpgrade(userInfo.level, userInfo.level + count)
                resource = obj.resource
                time=obj.time
            }
        }
        if (itemType == 'gen') {
            generalInfo = await this.generalEntity.findOneBy({ id: Number(itemId) })
            if (generalInfo.level >= userInfo.level) {
                errMsg = '请升级角色等级'
            }else{
                let obj=roleUpgrade(generalInfo.level, generalInfo.level + count)
                resource = obj.resource
                time=obj.time
            }

        }
        if (itemType == 'sol') {
            soldierInfo = await this.soldierEntity.findOne({
                where: { id: Number(itemId) },
                relations: ['general']
            });
            if (soldierInfo.level >= soldierInfo.general.level) {
                errMsg = '请升级武将等级'
            }else{
                let obj=roleUpgrade(soldierInfo.level, soldierInfo.level + count)
                resource = obj.resource
                time=obj.time
            }
        }
        if(count>1){
            let goodNum=await this.goodsService.getGoodNumByName(Number(userId),'升级牌')
            if(goodNum<1){
                errMsg='你没有升级牌'
            }
        }
        if (userInfo.food < resource || userInfo.iron < resource || userInfo.wood < resource || userInfo.stone < resource) {
            errMsg = `升级所需各资源为${resource}</br>你的资源为：粮草x${userInfo.food} 木材x${userInfo.wood} 石料x${userInfo.stone} 生铁x${userInfo.stone}`;
        }
        if (!errMsg) {
            try {
                await this.dataSource.transaction(async (manager) => {
                    userInfo.food -= resource
                    userInfo.iron -= resource
                    userInfo.wood -= resource
                    userInfo.stone -= resource
                    if(count>1){
                        //升多级
                        if(itemType=='role'){
                            userInfo.level+=count
                            await manager.save(userInfo)
                        }
                        if(itemType=='gen'){
                            generalInfo.level+=count
                            await manager.save(generalInfo)
                        }
                        if(itemType=='sol'){
                            soldierInfo.level+=count
                            await manager.save(soldierInfo)
                        }
                        await this.goodsService.changePersonGoodByName(userId,'升级牌',1,'sub')
                    }else{
                        if (itemType == 'role') userInfo.upgradeStatus = 2
                        await manager.save(RoleEntity, userInfo)
                        if (itemType == 'gen') await manager.save(GeneralEntity, { id: Number(itemId), upgradeStatus: 2 })
                        if (itemType == 'sol') await manager.save(SoldierEntity, { id: Number(itemId), upgradeStatus: 2 })
                        let manorEventInfo = new ManorEventEntity()
                        manorEventInfo.type = 4
                        manorEventInfo.buildId = Number(itemId)
                        manorEventInfo.userId = userId
                        manorEventInfo.num2 = count
                        manorEventInfo.roleType = itemType
                        manorEventInfo.completionTime = new Date(Date.now() + time * 1000)
                        manorEventInfo = await manager.save(ManorEventEntity, manorEventInfo)
                        await this.redis.hset('user' + userId, 'userInfo', JSON.stringify(userInfo))
                        await this.redis.hdel('user' + userId, 'token')
                        this.tasksService.createTimer(String(new Date(Date.now() + time * 1000)), 'upgrade' + manorEventInfo.id, () => {
                            //升级任务
                            this.createUpgradeTaskTimer(manorEventInfo.id)
                        })
                    }
                    
                });
            } catch (error) {
                errMsg = '升级失败'
            }
        }
        if (errMsg) {
            await this.redis.hdel('user' + userId, 'token')
            return await this.getMiddlePage(sid, cmd, String(userId), { msg: errMsg,hidden:true })
        }
        let content = `${count>1?'升级牌-1<br/>':''}`
        let routerInfo = await this.commonService.getNotHiddenRouter(userId)
        if(routerInfo.service=='general'){
            content += await this[routerInfo.name](sid, cmd, userId, routerInfo.params)
        }else{
            content += await this[routerInfo.service][routerInfo.name](sid, cmd, userId, routerInfo.params)
        }
        return content
    }
    //招募士兵提交
    async recruitSoldiersPost(userId: number, soldierId: number, count: number, cmd: number, sid: string) {
        count = Number(count);
        let msg = '', content = '';
        let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: userId });
        let soldierInfo = await this.getSoldierDetail(soldierId);
        let generalInfo = await this.getGeneralDetail(soldierInfo.general.id);
        let generalList = await this.getGeneralList(String(userId));
        let foodCount = soldierTemplate[soldierInfo.type].recruitFood * count
        let goldCount = soldierTemplate[soldierInfo.type].recruitGold * count
        if (!isPureNumber(count) || count < 1 || count > 9999) {
            msg = '数量不正确'
        } else {
            if (userInfo.count - userInfo.currentCount < count * soldierInfo.population) msg = '角色人口数量已达到上限'
            if (generalInfo.countAll - generalInfo.count < soldierInfo.population * count) msg = '武将人口数量已达到上限'
            if (foodCount > userInfo.food) msg = '你没有足够的粮草招募' + soldierInfo.name + '*' + count
            if (goldCount > userInfo.gold) msg = '你没有足够的银两招募' + soldierInfo.name + '*' + count
        }

        let routerList = JSON.parse(await this.redis.hget('user' + userId, 'routerList'))
        if (msg) {
            await this.redis.hdel('user' + userId, 'token')
            content = '<p style="color:red">' + msg + '</p>'
            let routerInfo = routerList.pop()
            content += await this[routerInfo.name](sid, cmd, userId, routerInfo.params)
            return content
        }
        try {
            await this.dataSource.transaction(async (manager) => {
                //更新用户信息
                //添加事件
                //添加循环任务
                //更新redis
                //添加日志
                userInfo.food -= foodCount
                userInfo.gold -= goldCount
                await manager.save(RoleEntity, userInfo)
                let manorEventInfo = new ManorEventEntity()
                manorEventInfo.type = 2
                manorEventInfo.taskType = 2
                manorEventInfo.completionTime = new Date(Date.now() + soldierTemplate[soldierInfo.type].recruitTime * 1000)
                manorEventInfo.loopTime = soldierTemplate[soldierInfo.type].recruitTime
                manorEventInfo.num2 = count
                manorEventInfo.buildId = soldierId
                manorEventInfo.userId = userId
                manorEventInfo = await manager.save(ManorEventEntity, manorEventInfo)
                await this.redis.hset('user' + userId, 'userInfo', JSON.stringify(userInfo))
                await this.redis.hdel('user' + userId, 'token')
                this.tasksService.createInterval(manorEventInfo.loopTime * 1000, 'recruit' + manorEventInfo.id, () => {
                    //招募任务
                    this.createRecruitTaskTimer(manorEventInfo.id)
                })
            });
        } catch (error) {
            console.log('事务出错', error);
            let routerInfo = routerList.pop()
            content = '未知错误' + await this[routerInfo.name](sid, cmd, userId, routerInfo.params)
            return content
        }
        //写入日志
        this.eventEmitter.emit('writeLogs', { userId, name: '招募士兵:' + soldierInfo.name, gold: goldCount, food: foodCount })
        let routerInfo = await this.commonService.getPrevRouter3(userId)
        content = `你用${goldCount}银两,${foodCount}粮草招募了${soldierInfo.name}×${count}</br>`
        content += await this[routerInfo.name](sid, cmd, userId, routerInfo.params)
        return content
    }
    //模板
    async moban(sid: string, cmd: number, userId: string) {
        let blessInfo=await this.blessEntity.findOneOrFail({where:{generalId:null}})
        console.log('blessInfo', blessInfo);
        // const params = {sid, cmd}
        // const urlObj = { [cmd]: { name: 'home', title: '游戏首页',service:'general', params:{} } }
        // let backRouter = await this.backRouter(userId, params, urlObj)
        // let content = ``
        // await this.redis.hset('user'+userId,'routers', JSON.stringify(urlObj))
        // await this.redis.expire('user'+userId, 1800); 
        // return content;
        return '模板'
    }
    //获取npc详情
    // async getNpcInfo(npcId: number) {
    //     let npcInfo = JSON.parse((await this.redis.hget('npcInfo', String(npcId)))?? '{}')
    //     if(Object.keys(npcInfo).length === 0){
    //         npcInfo=await this.npcEntity.findOne({where:{id:npcId}})
    //         await this.redis.hset('npcInfo', String(npcId), JSON.stringify(npcInfo))
    //     }
    //     return npcInfo
    // }
    //设置返回路由
    async backRouter(userId, params, urlObj) {
        //删除重复的路由
        function removeDuplicates(arr) {
            const names = new Set();
            names.add(urlObj[params.cmd].service+urlObj[params.cmd].name)
            return arr.filter(item => {
                let name=item.service+item.name
                if (names.has(name)) {
                    return false; // 重复项，过滤掉
                } else {
                    names.add(name);
                    return true; // 首次出现，保留
                }
            });

        }
        let str = ''
        //返回路由
        let routerList = JSON.parse(await this.redis.hget('user' + userId, 'routerList'))
        routerList.reverse().shift()
        routerList=removeDuplicates(routerList)
        routerList.forEach(element => {
            if (!element.params.hidden) {
                str += `<a href="${this.seturlOther(params, urlObj, element.title, element.name, element.params, element.service)}">返回${element.title}</a><br/>`
            }
        });
        return str
    }
    /**
     * 
     * @param params cnd sid参数 
     * @param urlObj url集合
     * @param title 页面标题
     * @param name 页面名称
     * @param param 请求参数
     * @param service 页面服务名
     * @returns string 页面url
}
     */
    seturlOther(params, urlObj, title: string, name: string, attr = {}, service = 'general') {
        params.cmd++
        urlObj[params.cmd] = { title, name, service, params: attr }
        return `/gcmd?sid=${params.sid}&cmd=${params.cmd}`
    }
    //添加武将 士兵 技能
    async addGeneralAll(userId: string, generalName: string, generalNameStr=null) {
        let generalList=await this.getGeneralList(userId)
        
        let soldierlArr: SoldierEntity[] = []
        let skillArr = []
        let generalInfo=null;
        if(generalNameStr){
            for (const key in generalTemplate) {
                if (generalTemplate[key].name===generalNameStr) {
                    generalInfo = generalTemplate[key]
                    break
                }
            }
        }else{
            for (const key in generalTemplate) {
                if (Object.prototype.hasOwnProperty.call(generalTemplate, key)) {
                    const element = generalTemplate[key];
                    if(element.name===generalName){
                        generalInfo = generalTemplate[key]
                        break
                    }
                }
            }
            
        }

        if(!generalInfo||generalList.find(item=>item.name==generalInfo.name)){
            return
        }
        let general = new GeneralEntity()
        general.name = generalInfo.name
        general.userId = Number(userId)
        //添加武将技能
        generalInfo.skill.forEach((item, index) => {
            let skillGeneral = new SkillEntity()
            skillGeneral.name = item[0]
            skillGeneral.damage = item[1]
            skillGeneral.distance = item[2]
            skillGeneral.damageNum = item[3]
            skillGeneral.weaponType = item[4]
            if (item[0] == '普通攻击') {
                skillGeneral.isDefault = 1
            }
            if (item[5]) {
                skillGeneral.interval = item[5]
                skillGeneral.type = 2
            }

            skillGeneral.general = general
            skillArr.push(skillGeneral)
        })
        //添加士兵
        generalInfo.soldier.forEach((item, index) => {
            let soldier = new SoldierEntity()
            soldier.name = item.name
            soldier.attack = item.attack
            soldier.hp = item.hp
            soldier.defense = item.defense
            soldier.population = item.population
            soldier.userId = Number(userId)
            soldier.general=general
            soldierlArr.push(soldier)
            //添加士兵技能
            item.skill.forEach((item, index) => {
                let skillSoldier = new SkillEntity()
                skillSoldier.name = item[0]
                skillSoldier.damage = item[1]
                skillSoldier.distance = item[2]
                skillSoldier.damageNum = item[3]
                skillSoldier.weaponType = item[4]
                if (item[5]) {
                    skillSoldier.interval = item[5]
                }
                if (item[0] == '普通攻击') {
                    skillSoldier.isDefault = 1
                }
                skillSoldier.soldier = soldier
                skillArr.push(skillSoldier)
            })
            // console.log('技能数组', skillArr);
        })
        await this.dataSource.transaction(async (manager) => {
            await manager.save(GeneralEntity, general)
            await manager.save(SoldierEntity, soldierlArr)
            await manager.insert(SkillEntity, skillArr)
        });
    }
    //添加武将 
    async addGeneral(userId: string, generalName: string, near: boolean = false) {

    }
    //添加士兵
    //添加技能
    //获取武将列表
    async getGeneralList(userId: string) {
        return await this.generalEntity.find({ where: { userId: Number(userId) } });
    }
    //获取士兵列表
    async getSoldierList(userId: string) {
        return await this.soldierEntity.find({ where: { userId: Number(userId) } });
    }
    //获取武将详情 关联士兵 技能
    async getGeneralDetail(generalId: number) {
        let generalDetail = await this.generalEntity.findOne({
            where: { id: generalId },
            relations: ['skills', 'soldiers', 'soldiers.skills', 'soldiers.equip']
        });
        // generalDetail.equip=await this.getEquipDetail(generalId)
        return generalDetail
    }
    //获取武将/士兵装备 type general或soldier
    // async getEquipDetail(id: number,type='general') {
    //     let equipList=await this.generalEquipEntity.find({where:{[type+'Id']:id}})
    //     return equipList
    // }
    //获取武将详情 关联士兵
    async getGeneralDetailByName(userId: number,generalName: string) {
        let generalDetail = await this.generalEntity.findOne({
            where: { userId, name: generalName },
            relations: ['soldiers']
        });
        return generalDetail
    }
    //获取士兵详情
    async getSoldierDetail(soldierId?: number) {
        let soldierDetail = await this.soldierEntity.findOne({
            where: { id: soldierId },
            relations: ['skills', 'equip','general']
        });
        return soldierDetail
    }

    //招募士兵任务
    @OnEvent('createRecruitTaskTimer')
    async createRecruitTaskTimer(manorEventId: number) {
        console.log('招募士兵任务');
        let manorEvent = await this.manorEventEntity.findOne({ where: { id: manorEventId } })
        if (manorEvent.status == 2){
            this.tasksService.clearInterval('recruit' + manorEventId)
            return
        }
        let soldierInfo = await this.getSoldierDetail(manorEvent.buildId);
        let userInfo: RoleEntity = await this.roleEntity.findOne({ where: { id: manorEvent.userId } })
        let generalInfo = await this.getGeneralDetail(soldierInfo.general.id);
        let generalList = await this.getGeneralList(String(manorEvent.userId));
        let soldierAllNumRole = 0;
        generalList.forEach(item => {
            soldierAllNumRole += item.count
        });
        
        if (userInfo.count - soldierAllNumRole < soldierInfo.population) {
            this.manorEventEntity.update(manorEventId, { completionTime: (new Date(Date.now()+manorEvent.loopTime*1000)) })
            return
        }
        if (generalInfo.countAll - generalInfo.count < soldierInfo.population) {
            this.manorEventEntity.update(manorEventId, { completionTime: (new Date(Date.now()+manorEvent.loopTime*1000)) })
            return
        }
        await this.dataSource.transaction(async (manager) => {
            
            //士兵人口+1 武将人口+1*占用人口
            await manager.update(ManorEventEntity, manorEventId, { num1: () => "num1 + " + 1,completionTime:(new Date(Date.now()+manorEvent.loopTime*1000)) })
            await manager.update(SoldierEntity, soldierInfo.id, { count: () => "count + " + 1 })
        })
        await this.commonService.generalPopulation(generalInfo.id)
        if(manorEvent.num1+1>=manorEvent.num2){
            this.manorEventEntity.update(manorEventId, { status: 2 })
            this.tasksService.clearInterval('recruit' + manorEventId)
            return
        }
    }
    //角色 士兵 武将升级
    @OnEvent('createUpgradeTaskTimer')
    async createUpgradeTaskTimer(manorEventId: number) {
        console.log('升级任务', manorEventId);
        let manorEvent = await this.manorEventEntity.findOne({ where: { id: manorEventId } })
        if (manorEvent.status == 2) return
        let entity = 'roleEntity'
        if (manorEvent.roleType == 'sol') {
            entity = 'soldierEntity'
        } else if (manorEvent.roleType == 'gen') {
            entity = 'generalEntity'
        }
        await this[entity].update(manorEvent.buildId, { level: () => 'level + ' + manorEvent.num2, upgradeStatus: 1})
        await this.manorEventEntity.update(manorEventId, { status: 2 })
        let obj={
            'sol':'soldier',
            'gen':'general',
            'role':'user'
        }
        //更新战力
        await this.calculatePower(obj[manorEvent.roleType], manorEvent.buildId)
        if(manorEvent.roleType=='gen'){
            await this.commonService.generalPopulation(manorEvent.buildId)
        }
        if (entity == 'roleEntity') {
            let userInfo=await this.roleEntity.findOne({ where: { id: manorEvent.buildId } })
            let maxWeight=(userInfo.level+1)*25+60
            userInfo.maxWeight=maxWeight
            await this.roleEntity.save(userInfo)
        }
    }
    //中间确认页 提示内容 页面 服务
    async confirmPage(sid: string, cmd: number, userId: string, { msg,pageName,serviceName,other}) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'confirmPage', title: '提示', service: 'general', params: { msg,pageName,serviceName,other } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content =`
        ${msg}<br/>
        <a href="${this.seturlOther(params, urlObj, '确定', pageName, other, serviceName)}">确定</a><br/>
        ${backRouter}
        `
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //中间页 提示
    async getMiddlePage(sid: string, cmd: number, userId: string, { msg,hidden=true }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'getMiddlePage', title: '提示', service: 'general', params: { hidden: true,msg } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = msg + '<br/>' + backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    setpage(params, urlObj, page, totalItems, attrs, router, routerName?: string) {
        let pageStr = '';
        let totalPages = Math.ceil(totalItems / this.size);
        const createPageLink = (newPage, text) => {
            let newAttrs = { ...attrs, page: newPage };
            pageStr += `<a href="${this.seturlOther(params, urlObj, routerName, router, newAttrs)}">${text}</a> `;
        };
        if (page > 2) createPageLink(1, '首页');
        if (page !== 1) createPageLink(page - 1, '上一页');
        if (page < totalPages) createPageLink(page + 1, '下一页');
        if (page < totalPages - 1 && totalPages > 1) createPageLink(totalPages, '末页');
        return pageStr
    }
}
