import {Entity , Column ,PrimaryGeneratedColumn,CreateDateColumn,UpdateDateColumn, Index,ManyToOne, OneToOne} from 'typeorm'
//庄园事件
@Entity()
export class ManorEventEntity{
  
  @PrimaryGeneratedColumn()
  id:number
  //类型  1建筑 2招募 3打造 4升级
  @Column({ type: 'int'})
  type:number
  //角色类型 sol gen role  要打造的物品名称
  @Column({length:4, nullable:true})
  roleType:string
  //建筑id 士兵/武将id
  @Column({ type: 'int'})
  @Index()
  buildId:number
  //任务类型  1定时任务 2循环任务
  @Column({ type: 'int',default:1 })
  taskType:number
  //完成时间
  @Column({ type: 'timestamp',nullable:true})
  completionTime:Date
  //循环时间 单位秒
  @Column({ type: 'int',nullable:true})
  loopTime:number
    //用户id
  @Index()
  @Column({ type: 'int'})
  userId:number
  
  //已招募数量 已打造的数量
  @Column({ type: 'int',default:0 })
  num1:number
  //总招募数量  总打造数量 升级等级
  @Column({ type: 'int',default:0 })
  num2:number
  //状态 1未处理 2已处理
  @Index()
  @Column({ type: 'int',default:1 })
  status:number
  //时间
  @CreateDateColumn({name:'create_date',type: 'timestamp'})
    createDate: Date;
}
/**
 * 建筑：建筑id，完成时间，玩家id   升级到的等级
 * 招募：id，完成事件，玩家id，    已招募数量，总招募数量，武将/士兵，
 * 打造：建筑id，完成时间，玩家id  已打造的数量，总打造数量
 */