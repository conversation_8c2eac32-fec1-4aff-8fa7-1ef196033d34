import { Injectable } from '@nestjs/common';
import { CreateAdminDto } from './dto/create-admin.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { MapsEntity } from 'src/entities/maps.entity';
import { Equal, In, Like, MoreThan, Not, Repository } from 'typeorm';
import { NpcEntity } from 'src/entities/npc.entity';
import { UpdateAdminDto } from './dto/update-admin.dto';
import { NpcAdminDto } from './dto/npc-admin.dto';
import { GeneralAdminDto } from './dto/general-admin.dto';
import { SoldierAdminDto } from './dto/soldier-admin.dto';
import { NpcGeneralEntity } from 'src/entities/npcGeneral.entity';
import { NpcSoldierEntity } from 'src/entities/npcSoldier.entity';
import { GoodEntity } from 'src/entities/goods.entity';
import { GoodAdminDto } from './dto/good-admin.dto';
import { getRandomNumber, roleUpgrade } from 'src/utils/config';
import { GeneralEntity } from 'src/entities/general.entity';
import { NpcGoodsEntity } from 'src/entities/npcGoods.entity';
import { RedisService } from 'src/middleware/redis.service';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { RoleEntity } from 'src/entities/role.entity';

@Injectable()
export class AdminService {
  constructor(
    private readonly redisService: RedisService,
    @InjectRepository(MapsEntity) private readonly mapsEntity: Repository<MapsEntity>,
    @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
    @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
    @InjectRepository(NpcEntity) private readonly npcEntity: Repository<NpcEntity>,
    @InjectRepository(NpcSoldierEntity) private readonly npcSoldierEntity: Repository<NpcSoldierEntity>,
    @InjectRepository(NpcGeneralEntity) private readonly npcGeneralEntity: Repository<NpcGeneralEntity>,
    @InjectRepository(NpcGoodsEntity) private readonly npcGoodsEntity: Repository<NpcGoodsEntity>,
    @InjectRepository(NpcGeneralEntity) private readonly generalEntity: Repository<NpcGeneralEntity>,
    @InjectRepository(NpcSoldierEntity) private readonly soldierEntity: Repository<NpcSoldierEntity>,
    @InjectRepository(GoodEntity) private readonly goodEntity: Repository<GoodEntity>,
  ) { }
  // 获取指定范围地图
  async getMaps(createAdminDto: CreateAdminDto) {
    let maps = await this.mapsEntity.find({ select: ['id', 'title', 'desc', 'topId', 'bottomId', 'leftId', 'rightId'] })
    function getRelatedMaps(id, mapData, depth = 5) {
      const result = [];
      const visited = new Set();

      function traverse(currentId, currentDepth) {
        if (visited.has(currentId) || currentDepth === 0) return; // 如果 id 已被访问或深度达到限制，则跳过
        visited.add(currentId);

        // 在 mapData 中查找当前 id 的记录
        const currentMap = mapData.find(map => map.id === currentId);
        if (currentMap) {
          result.push(currentMap);

          // 递归检查关联的 id，减少深度
          traverse(currentMap.topId, currentDepth - 1);
          traverse(currentMap.leftId, currentDepth - 1);
          traverse(currentMap.rightId, currentDepth - 1);
          traverse(currentMap.bottomId, currentDepth - 1);
        }
      }

      traverse(id, depth);
      return result;
    }

    const relatedMaps = getRelatedMaps(createAdminDto.id, maps);
    return relatedMaps;
  }
  //添加地图
  async addMap(createAdminDto: CreateAdminDto) {
    let mapEntity = new MapsEntity()
    mapEntity.title = createAdminDto.title
    mapEntity.desc = createAdminDto.desc
    await this.mapsEntity.save(mapEntity)
    createAdminDto.id = mapEntity.id
    let res = await this.lineMap(createAdminDto)
    if (res && res.length) {
      return { code: 200, msg: '修改成功' }
    } else {
      return { code: 500, msg: '修改失败' }
    }
  }
  //连线
  async lineMap(createAdminDto: CreateAdminDto) {
    let sourceName = '', name = '', sourceTitle = '', title = '';
    switch (createAdminDto.directionType) {
      case 'top':
        sourceName = 'bottomId'
        name = 'topId'
        break
      case 'bottom':
        sourceName = 'topId'
        name = 'bottomId'
        break
      case 'left':
        sourceName = 'rightId'
        name = 'leftId'
        break
      case 'right':
        sourceName = 'leftId'
        name = 'rightId'
        break
    }
    let mapEntity = new MapsEntity()
    mapEntity.id = createAdminDto.sourceId
    mapEntity[name] = createAdminDto.id
    let mapEntity1 = new MapsEntity()
    mapEntity1.id = createAdminDto.id
    mapEntity1[sourceName] = createAdminDto.sourceId
    let res = await this.mapsEntity.save([mapEntity, mapEntity1])
    return res
  }
  //断开连接
  async deleteLine(createAdminDto: CreateAdminDto) {
    const reverseDirection = (direction: string) => {
      switch (direction) {
        case "topId":
          return "bottomId";
        case "bottomId":
          return "topId";
        case "leftId":
          return "rightId";
        case "rightId":
          return "leftId";
      }
    }
    let res = await this.mapsEntity.findOneBy({ id: createAdminDto.id })
    let mapEntity = new MapsEntity()
    mapEntity.id = createAdminDto.id
    mapEntity[createAdminDto.directionType] = null
    let mapEntity1 = new MapsEntity()
    mapEntity1.id = res[createAdminDto.directionType]
    let d = reverseDirection(createAdminDto.directionType)
    mapEntity1[d] = null
    let result = await this.mapsEntity.save([mapEntity, mapEntity1])
    if (result && result.length) {
      return { code: 200, msg: '修改成功' }
    } else {
      return { code: 500, msg: '修改失败' }
    }
  }
  //删除地图
  async deleteMap(createAdminDto: CreateAdminDto) {
    //删除地图前删除连线
    let res = await this.mapsEntity.findOneBy({ id: createAdminDto.id })
    if (res.leftId) {
      await this.mapsEntity.update({ id: res.leftId }, { rightId: null })
    }
    if (res.rightId) {
      await this.mapsEntity.update({ id: res.rightId }, { leftId: null })
    }
    if (res.topId) {
      await this.mapsEntity.update({ id: res.topId }, { bottomId: null })
    }
    if (res.bottomId) {
      await this.mapsEntity.update({ id: res.bottomId }, { topId: null })
    }
    let npcList = await this.npcEntity.find({ where: { gpsId: createAdminDto.id } })
    if (npcList.length) {
      return { code: 500, msg: '删除失败' }
    }
    let res1 = await this.mapsEntity.delete({ id: createAdminDto.id })
    if (res1.affected) {
      return { code: 200, msg: '删除成功' }
    } else {
      return { code: 500, msg: '删除失败' }
    }
  }
  //修改map
  async updateMap(createAdminDto: CreateAdminDto) {

    let res = await this.mapsEntity.update({ id: createAdminDto.id }, {
      title: createAdminDto.title,
      desc: createAdminDto.desc,
      leftId: createAdminDto.leftId,
      rightId: createAdminDto.rightId,
      topId: createAdminDto.topId,
      bottomId: createAdminDto.bottomId,
      dungeonName: createAdminDto.dungeonName,
      conditionType: createAdminDto.conditionType,
      mapName: createAdminDto.mapName,
      goodName: createAdminDto.goodName,
      maxCount: createAdminDto.maxCount,
      msg: createAdminDto.msg,
      showMapName: createAdminDto.showMapName,
    })
    if (res.affected) {
      return { code: 200, msg: '修改成功' }
    } else {
      return { code: 500, msg: '修改失败' }
    }
  }
  //获取地图列表
  async getList(createAdminDto: CreateAdminDto) {
    //分页
    let where: any = {}
    if (createAdminDto.title) {
      //模糊匹配
      where.title = Like(`%${createAdminDto.title}%`)
      // where.title=createAdminDto.title
    }
    if (createAdminDto.id) {
      where.id = createAdminDto.id
    }
    let [list, total] = await this.mapsEntity.findAndCount({
      where,
      skip: (createAdminDto.page - 1) * createAdminDto.size,
      take: createAdminDto.size,
      order: {
        id: 'DESC'
      }
    })
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.leftId) {
        let res = await this.mapsEntity.findOneBy({ id: item.leftId })
        item.left = res.title
      }
      if (item.rightId) {
        let res1 = await this.mapsEntity.findOneBy({ id: item.rightId })
        item.right = res1.title
      }
      if (item.topId) {
        let res2 = await this.mapsEntity.findOneBy({ id: item.topId })
        item.top = res2.title
      }
      if (item.bottomId) {
        let res3 = await this.mapsEntity.findOneBy({ id: item.bottomId })
        item.bottom = res3.title
      }
    }
    return { code: 200, data: list, total }
  }
  //获取npc
  async getNpc(createAdminDto: CreateAdminDto) {
    let where: any = { gpsId: createAdminDto.id }

    if (createAdminDto.title) {
      // 模糊匹配
      where.name = Like(`%${createAdminDto.title}%`)
    }

    let [npcs, total] = await this.npcEntity.findAndCount({
      where,
      skip: (createAdminDto.page - 1) * createAdminDto.size,
      take: createAdminDto.size,
      order: {
        id: 'DESC'
      }
    })

    return { code: 200, data: npcs, total }
  }
  //获取武将
  async getGeneral(createAdminDto: CreateAdminDto) {
    let res = await this.npcEntity.findOne({ where: { id: createAdminDto.id }, relations: ['generals'] })
    if (res) {
      return { code: 200, data: res.generals }
    } else {
      return { code: 500, msg: '获取失败' }
    }
  }
  //获取士兵
  async getSoldier(createAdminDto: CreateAdminDto) {
    let res = await this.generalEntity.findOne({ where: { id: createAdminDto.id }, relations: ['soldiers'] })
    if (res) {
      return { code: 200, data: res.soldiers }
    } else {
      return { code: 500, msg: '获取失败' }
    }
  }
  // //获取武将技能
  // async getGeneralSkill(createAdminDto: CreateAdminDto){
  //   let params=createAdminDto.id?{id:createAdminDto.id}:{}
  //   let res=await this.generalEntity.findOne({where:params,relations:['skills']})
  //   if(res){
  //     return {code:200,data:res.skills}
  //   }else{
  //     return {code:500,msg:'获取失败'}
  //   }
  // }
  ////获取士兵技能
  // async getSoldierSkill(createAdminDto: CreateAdminDto){
  //   let res=await this.soldierEntity.findOne({where:{id:createAdminDto.id},relations:['skills']})
  //   if(res){
  //     return {code:200,data:res.skills}
  //   }else{
  //     return {code:500,msg:'获取失败'}
  //   }
  // }
  // //获取技能模板列表
  // async getSkillTemplate(createAdminDto: CreateAdminDto){
  //   let res=await this.skillTemplateEntity.find({where:createAdminDto.title && { name: createAdminDto.title }})
  //   if(res){
  //     return {code:200,data:res}
  //   }else{
  //     return {code:500,msg:'获取失败'}
  //   }
  // }
  // //添加技能模板
  // async addSkillTemplate(updateAdminDto: UpdateAdminDto){
  //   let res=await this.skillTemplateEntity.save(updateAdminDto)
  //   if(res){
  //     return {code:200,msg:'添加成功'}
  //   }else{
  //     return {code:500,msg:'添加失败'}
  //   }
  // }
  // //删除技能模板
  // async delSkillTemplate(createAdminDto: CreateAdminDto){
  //   let res=await this.skillTemplateEntity.delete({id:createAdminDto.id})
  //   if(res){
  //     return {code:200,msg:'删除成功'}
  //   }else{
  //     return {code:500,msg:'删除失败'}
  //   }
  // }
  // //添加修改技能
  // async addSkill(updateAdminDto: UpdateAdminDto){
  //   let res=await this.skillEntity.save({...updateAdminDto})
  //   if(res){
  //     return {code:200,msg:'添加成功'}
  //   }else{
  //     return {code:500,msg:'添加失败'}
  //   }
  // }
  //添加修改npc
  async addNpc(npcAdminDto: NpcAdminDto) {
    let ran = getRandomNumber(0.8, 0.9)
    let params: any = Object.assign({}, npcAdminDto)
    params.level = params.level || 1
    params.food = params.wood = Math.floor((params.level) * 15 * ran)
    params.stone = params.iron = Math.floor((params.level) * 13 * ran)
    params.potential = Math.floor((params.level) * 1400 * ran)
    params.gold = Math.floor((params.level) * 25 * ran)
    console.log(params)
    let res = await this.npcEntity.save(params)
    if (res) {
      return { code: 200, msg: '添加成功' }
    } else {
      return { code: 500, msg: '添加失败' }
    }
  }
  //删除npc
  async delNpc(createAdminDto: CreateAdminDto) {
    const npc = await this.npcEntity.findOne({
      where: { id: createAdminDto.id },
      relations: ['generals', 'npcGoods', 'generals.soldiers']
    });
    if (npc.generals.length) {
      for (let i = 0; i < npc.generals.length; i++) {
        let general = npc.generals[i]
        if (general.soldiers.length) {
          await this.npcSoldierEntity.delete({ id: In(general.soldiers.map(item => item.id)) })
        }
        await this.npcGeneralEntity.delete(general.id)
      }
    }
    if (npc.npcGoods.length) {
      await this.npcGoodsEntity.delete({ id: In(npc.npcGoods.map(item => item.id)) })
    }

    if (!npc) {
      return { code: 404, msg: 'NPC不存在' };
    }

    const res = await this.npcEntity.remove(npc);
    if (res) {
      return { code: 200, msg: '删除成功' };
    } else {
      return { code: 500, msg: '删除失败' };
    }
  }
  //添加武将
  async addGeneral(generalAdminDto: GeneralAdminDto) {
    generalAdminDto.countAll = generalAdminDto.count
    generalAdminDto.hpNow = generalAdminDto.hp
    generalAdminDto.npc = { id: generalAdminDto.npcId }
    let res = await this.generalEntity.save(generalAdminDto)
    if (res) {
      return { code: 200, msg: '添加成功' }
    } else {
      return { code: 500, msg: '添加失败' }
    }
  }
  //删除武将
  async delGeneral(createAdminDto: CreateAdminDto) {
    let res = await this.generalEntity.delete({ id: createAdminDto.id })
    if (res) {
      return { code: 200, msg: '删除成功' }
    } else {
      return { code: 500, msg: '删除失败' }
    }
  }
  //添加修改士兵
  async addSoldier(soldierAdminDto: SoldierAdminDto) {
    soldierAdminDto.general = { id: soldierAdminDto.generalId }
    let res = await this.soldierEntity.save(soldierAdminDto)
    if (res) {
      return { code: 200, msg: '添加成功' }
    } else {
      return { code: 500, msg: '添加失败' }
    }
  }
  //删除士兵
  async delSoldier(createAdminDto: CreateAdminDto) {
    let res = await this.soldierEntity.delete({ id: createAdminDto.id })
    if (res) {
      return { code: 200, msg: '删除成功' }
    } else {
      return { code: 500, msg: '删除失败' }
    }
  }
  //复制npc 只复制一个武将的
  async copyNpc(createAdminDto) {
    let npcId = createAdminDto.npcId;
    let npcInfo = await this.npcEntity.findOne({ where: { id: npcId }, relations: ['generals', 'generals.soldiers', 'npcGoods'] })

    //npc
    delete npcInfo.id
    npcInfo.generals.forEach((general) => {
      delete general.id
      general.soldiers.forEach((soldier) => {
        delete soldier.id
      })
    })
    npcInfo.npcGoods.forEach((good) => {
      delete good.id
    })
    npcInfo.gpsId = createAdminDto.gpsId
    let res = await this.npcEntity.save(npcInfo)
    if (res) {
      return { code: 200, msg: '复制成功' }
    } else {
      return { code: 500, msg: '复制失败' }
    }
  }
  //物品列表
  async getGoodsList(createAdminDto: CreateAdminDto) {
    //分页
    let where:any={}
    if(createAdminDto.title){
      where.name=Like(`%${createAdminDto.title}%`)
    }
    let [goods, total] = await this.goodEntity.findAndCount({
      where,
      skip: (createAdminDto.page - 1) * createAdminDto.size,
      take: createAdminDto.size,
      order: {
        id: 'DESC'
      }
    })

    return { code: 200, data: goods, total }
  }
  //添加物品
  async addGood(generalAdminDto: GoodAdminDto) {
    let res = await this.goodEntity.save(generalAdminDto)
    if (res) {
      return { code: 200, msg: '添加成功' }
    } else {
      return { code: 500, msg: '添加失败' }
    }
  }
  //删除物品
  async delGood(createAdminDto: CreateAdminDto) {
    let res = await this.goodEntity.delete({ id: createAdminDto.id })
    if (res) {
      return { code: 200, msg: '删除成功' }
    } else {
      return { code: 500, msg: '删除失败' }
    }
  }
  //赠送物品
  async sendGood(createAdminDto: CreateAdminDto) {
    let userInfo=await this.roleEntity.findOneBy({name:createAdminDto.userName})
    if(!userInfo){
      return { code: 500, msg: '玩家不存在' }
    }
    let personGood=await this.personGoodsEntity.findOne({where:{userId:userInfo.id,good:{id:createAdminDto.goodId}}})
    if(personGood){
      personGood.count+=createAdminDto.num
      await this.personGoodsEntity.save(personGood)
      return { code: 200, msg: '赠送成功' }
    }else{
      personGood=new PersonGoodsEntity()
      personGood.userId=userInfo.id
      personGood.good=await this.goodEntity.findOneBy({id:createAdminDto.goodId})
      personGood.count=createAdminDto.num
      await this.personGoodsEntity.save(personGood)
      return { code: 200, msg: '赠送成功' }
    }
  }
  //添加npc 
  async addNpcs() {
    


    const npcObj = {}
const handelzhuangbei=(i)=>{
  let zhuangbei=['帽','衣','项链','手套','戒指','裤','鞋']
  let zhuangbeiName=['玉缕','追风','踏云','玉清','残阳','星辰','神行','麒麟','龙鳞']
  let arr=[]
  let name=zhuangbeiName[i-1]
  zhuangbei.forEach((item,index)=>{
      arr.push({
          name:name+item,
          min:index*4,
          max:index*4+3
      })
  })
  return arr  
}
    let npcList = []
    let skillList = []
    Object.keys(npcObj).forEach((key, index) =>  {
      let npm = new NpcEntity()
      npm.name = npcObj[key].名称
      npm.directAttack = npcObj[key].直接攻击 == '否' ? 0 : 1
      npm.canAttack = npcObj[key].可攻击 == '否' ? 0 : 1
      npm.canBuy = npcObj[key].可购买 == '否' ? 0 : 1
      npm.intervalTime = npcObj[key]?.杀死间隔 || 0
      npm.desc = npcObj[key].描述
      npm.gpsId = 1
      // npm.type='a'
      npm.generals = []
      npm.npcGoods = []
      let generalList = []
      npcObj[key].武将.forEach(general => {
        let gen = new NpcGeneralEntity()
        gen.name = general.名称
        gen.level = general.等级
        gen.attack = general.攻击
        gen.potential = general.等级
        gen.defense = general.防御
        gen.hp = general.血量
        gen.hpNow = general.血量
        gen.wuqiStr = general.兵器
        gen.fangjuStr = general.防具
        gen.skillStr = general.技能名称
        if (general.技能名称) skillList.push(general.技能名称)
        gen.soldiers = []
        let population = 0, count = 0;
        general.士兵.forEach(soldier => {
          if (soldier.名称) {
            let sol = new NpcSoldierEntity()
            sol.name = soldier.名称
            sol.level = soldier.等级
            sol.attack = soldier.攻击
            sol.defense = soldier.防御
            sol.hp = soldier.血量
            sol.population = soldier.占用人口
            sol.count = soldier.士兵数量
            sol.wuqiStr = soldier.兵器
            sol.fangjuStr = soldier.防具
            sol.skillStr = soldier.技能名称
            if (soldier.技能名称) skillList.push(soldier.技能名称)
            population += soldier.士兵数量
            count += (soldier.士兵数量 * soldier.占用人口)
            gen.soldiers.push(sol)
          }
        })
        gen.population = population
        gen.count = count
        gen.countAll = count
        generalList.push(gen)
      })
      npm.generals = generalList
      npcObj[key].掉落物品.forEach(good => {
        if (good.名称) {
          let goodEntity = new NpcGoodsEntity()
          goodEntity.goodName = good.名称
          goodEntity.count = good.数量
          goodEntity.min = (good.概率.split('-'))[0]
          goodEntity.max = (good.概率.split('-'))[1]
          npm.npcGoods.push(goodEntity)
        }
      })
      //临时开始
      
      // handelzhuangbei(Math.ceil((index+1)/2)).forEach(good=>{
      //   let goodEntity = new NpcGoodsEntity()
      //   goodEntity.goodName = good.name
      //   goodEntity.count = 1
      //   goodEntity.min = good.min
      //   goodEntity.max = good.max
      //   npm.npcGoods.push(goodEntity)
      // })
      //临时结束
      npcList.push(npm)
    })
    console.log('npcList',npcList);
    let res = await this.npcEntity.save(npcList)
    if (res) {
      return { code: 200, msg: '添加成功' }
    } else {
      return { code: 500, msg: '添加失败' }
    }
  }
  //复制掉落物品
  async copyGoods() {
    let temNpcIdArr = await this.npcEntity.find({ where: { gpsId: 1 }, select: ['id', 'name'] })
    let npcIdArr = await this.npcEntity.find({ where: { id: MoreThan(36), gpsId: Not(1), name: Equal('巨蛇') }, select: ['id', 'name'] }) as any
    npcIdArr.forEach(item => {
      let tem = temNpcIdArr.find(tem => tem.name == item.name)
      if (tem) {
        item.temId = tem.id
      }
    })
    for (let i = 0; i < npcIdArr.length; i++) {
      let npc = npcIdArr[i]
      let npcGoodsInfo = await this.npcGoodsEntity.find({ where: { npc: { id: npc.temId } } })
      delete npc.temId
      if (npcGoodsInfo.length) {
        npcGoodsInfo.forEach(item => {
          item.npc = npc
          item.id = undefined
        })
        await this.npcGoodsEntity.save(npcGoodsInfo)
      }
    }
    return '成功'
  }
  //根据地图名称拿到所有npcid
  async getNpcIdByMapName(createAdminDto: CreateAdminDto) {
    let mapInfo = await this.mapsEntity.find({ where: { title: '云树林' }, select: ['id'] })
    let res = await this.npcEntity.find({ where: { gpsId: In(mapInfo.map(item => item.id)) }, relations: ['generals'] })
    console.log('地图名称：', createAdminDto.title, '地图数量：', res.length)
    let npcIdArr = []
    res.forEach(item => {
      item.generals.forEach(general => {
        npcIdArr.push(general.id)
      })
    })
    return npcIdArr
  }
  //清理redis缓存
  async clearRedisCache() {
    let res = await this.redisService.delAll()
    return { code: 200, msg: '清理成功' }
  }
  //获取掉落物品列表
  async getDropGoods(createAdminDto: CreateAdminDto){
    let where:any={
      npc:{id:createAdminDto.id},
    }
    if(createAdminDto.title){
      where.goodName=Like(`%${createAdminDto.title}%`)
    }
    let [goods, total]=await this.npcGoodsEntity.findAndCount({
      where,
      relations:['npc'],
      skip: (createAdminDto.page - 1) * createAdminDto.size,
      take: createAdminDto.size,
      order: {
        id: 'DESC'
      }
    })
    return { code: 200, data: goods, total }
  }
  async delDropGoods(createAdminDto: CreateAdminDto){
    let res=await this.npcGoodsEntity.delete({ id: createAdminDto.id })
    if (res) {
      return { code: 200, msg: '删除成功' }
    } else {
      return { code: 500, msg: '删除失败' }
    }
  }
  async addDropGoods(createAdminDto: CreateAdminDto){
    let dropGood=new NpcGoodsEntity()
    dropGood.npc=await this.npcEntity.findOneBy({id:createAdminDto.npcId})
    dropGood.goodName=createAdminDto.goodName
    dropGood.count=createAdminDto.num
    dropGood.min=createAdminDto.min
    dropGood.max=createAdminDto.max
    let res=await this.npcGoodsEntity.save(dropGood)
    if (res) {
      return { code: 200, msg: '添加成功' }
    } else {
      return { code: 500, msg: '添加失败' }
    }
  }
  //千年古墓插入
  async addDungeon(){
    // const categorizedMonsters = {
    //   // 千年古墓
    //   '千年古墓一': ['古墓士兵', '古墓队长'],
    //   '千年古墓二': ['古墓百夫长', '古墓千夫长'],
    //   '千年古墓三': ['古墓副将', '古墓副帅'],
    //   '千年古墓四': ['古墓将军', '古墓元帅'],
    //   '千年古墓五': ['古墓千年百夫长', '古墓千年千夫长'],
    //   '千年古墓六': ['古墓千年副将', '古墓千年副帅'],
    //   '千年古墓七': ['古墓千年将军', '古墓千年元帅'],
    //   '千年古墓八': ['古墓万年将军', '古墓万年元帅'],
    //   '千年古墓九': ['古墓万年大将军', '古墓万年大元帅'],
    
    //   // 百兽山岭
    //   '百兽岭一': ['地鼠怪', '大地鼠怪'],
    //   '百兽岭二': ['狡兔怪', '大狡兔怪'],
    //   '百兽岭三': ['银狐怪', '大银狐怪'],
    //   '百兽岭四': ['巨狼怪', '大巨狼怪'],
    //   '百兽岭五': ['花豹怪', '大花豹怪'],
    //   '百兽岭六': ['猛虎怪', '大猛虎怪'],
    //   '百兽岭七': ['巨犀怪', '大巨犀怪'],
    //   '百兽岭八': ['雄狮怪', '大雄狮怪'],
    //   '百兽岭九': ['狂象怪', '大狂象怪'],
    
    //   // 狂盗山谷
    //   '狂盗谷一': ['狂盗谷混混', '狂盗谷大混混'],
    //   '狂盗谷二': ['狂盗谷地痞', '狂盗谷大地痞'],
    //   '狂盗谷三': ['狂盗谷小偷', '狂盗谷神偷'],
    //   '狂盗谷四': ['狂盗谷逃犯', '狂盗谷通缉犯'],
    //   '狂盗谷五': ['狂盗谷大盗', '狂盗谷飞天大盗'],
    //   '狂盗谷六': ['狂盗谷凶神', '狂盗谷大凶神'],
    //   '狂盗谷七': ['狂盗谷霸王', '狂盗谷大霸王'],
    //   '狂盗谷八': ['狂盗谷恶煞', '狂盗谷大恶煞'],
    //   '狂盗谷九': ['狂盗谷三当家', '狂盗谷二当家'],
    
    //   // 万剑宝塔
    //   '万剑塔一': ['炼剑童', '守剑童'],
    //   '万剑塔二': ['守剑弟子', '炼剑弟子'],
    //   '万剑塔三': ['守剑大弟子', '炼剑大弟子'],
    //   '万剑塔四': ['守剑师傅', '炼剑师傅'],
    //   '万剑塔五': ['守剑将军', '炼剑将军'],
    //   '万剑塔六': ['守剑元帅', '炼剑元帅'],
    //   '万剑塔七': ['守剑宗师', '炼剑宗师'],
    //   '万剑塔八': ['守剑神奴', '炼剑神奴'],
    //   '万剑塔九': ['守剑之神', '炼剑之神'],
    // };
    // const npcIdMap = {
    //   "古墓士兵": 1589,
    //   "古墓队长": 1590,
    //   "古墓百夫长": 1591,
    //   "古墓千夫长": 1592,
    //   "古墓副将": 1593,
    //   "古墓副帅": 1594,
    //   "古墓将军": 1595,
    //   "古墓元帅": 1596,
    //   "古墓千年百夫长": 1597,
    //   "古墓千年千夫长": 1598,
    //   "古墓千年副将": 1599,
    //   "古墓千年副帅": 1600,
    //   "古墓千年将军": 1601,
    //   "古墓千年元帅": 1602,
    //   "古墓万年将军": 1603,
    //   "古墓万年元帅": 1604,
    //   "古墓万年大将军": 1605,
    //   "古墓万年大元帅": 1606,
    //   "地鼠怪": 1607,
    //   "大地鼠怪": 1608,
    //   "狡兔怪": 1609,
    //   "大狡兔怪": 1610,
    //   "银狐怪": 1611,
    //   "大银狐怪": 1612,
    //   "巨狼怪": 1613,
    //   "大巨狼怪": 1614,
    //   "花豹怪": 1615,
    //   "大花豹怪": 1616,
    //   "猛虎怪": 1617,
    //   "大猛虎怪": 1618,
    //   "巨犀怪": 1619,
    //   "大巨犀怪": 1620,
    //   "雄狮怪": 1621,
    //   "大雄狮怪": 1622,
    //   "狂象怪": 1623,
    //   "大狂象怪": 1624,
    //   "狂盗谷混混": 1625,
    //   "狂盗谷大混混": 1626,
    //   "狂盗谷地痞": 1627,
    //   "狂盗谷大地痞": 1628,
    //   "狂盗谷小偷": 1629,
    //   "狂盗谷神偷": 1630,
    //   "狂盗谷逃犯": 1631,
    //   "狂盗谷通缉犯": 1632,
    //   "狂盗谷大盗": 1633,
    //   "狂盗谷飞天大盗": 1634,
    //   "狂盗谷凶神": 1635,
    //   "狂盗谷大凶神": 1636,
    //   "狂盗谷霸王": 1637,
    //   "狂盗谷大霸王": 1638,
    //   "狂盗谷恶煞": 1639,
    //   "狂盗谷大恶煞": 1640,
    //   "狂盗谷三当家": 1641,
    //   "狂盗谷二当家": 1642,
    //   "炼剑童": 1643,
    //   "守剑童": 1644,
    //   "守剑弟子": 1645,
    //   "炼剑弟子": 1646,
    //   "守剑大弟子": 1647,
    //   "炼剑大弟子": 1648,
    //   "守剑师傅": 1649,
    //   "炼剑师傅": 1650,
    //   "守剑将军": 1651,
    //   "炼剑将军": 1652,
    //   "守剑元帅": 1653,
    //   "炼剑元帅": 1654,
    //   "守剑宗师": 1655,
    //   "炼剑宗师": 1656,
    //   "守剑神奴": 1657,
    //   "炼剑神奴": 1658,
    //   "守剑之神": 1659,
    //   "炼剑之神": 1660
    // }
    // let arr=Object.keys(categorizedMonsters)
    // for(let i=0;i<arr.length;i++){
    //   let mapList=await this.mapsEntity.find({where:{title:Like(`%${arr[i]}%`)},select:['id','title']})
    //   for(let j=0;j<mapList.length;j++){
    //     let guaiwu=categorizedMonsters[arr[i]]
    //     for(let k=0;k<guaiwu.length;k++){
    //       let npcName=guaiwu[k]
    //       console.log('npc名称 ：',npcName,'npcid：',npcIdMap[npcName],'地图名称：',mapList[j].title,'地图id：',mapList[j].id);
    //       if(k==0){
    //         await this.copyNpc({npcId:npcIdMap[npcName],gpsId:mapList[j].id})
    //       }
    //       await this.copyNpc({npcId:npcIdMap[npcName],gpsId:mapList[j].id})
    //     }
    //   }
    // }
    return '成功'
  }
}
