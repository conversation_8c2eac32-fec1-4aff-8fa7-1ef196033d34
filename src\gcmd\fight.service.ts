import { ManorEntity } from 'src/entities/manor.entity';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RoleEntity } from 'src/entities/role.entity';
import { DataSource, Equal, In, IsNull, MoreThan, Not, Repository } from 'typeorm';
import { buildconfig, formatDuring,getRandomInt,getRandomNumber,goodsType,manorEventfn,extractBetween, formatDate, isPureNumber, weaponType } from 'src/utils/config';
import { ManorEventEntity } from 'src/entities/manorEvent.entity';
import { LogsEntity } from 'src/entities/logs.entity';
import { TasksService } from 'src/tasks/tasks.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { GeneralService } from './general.service';
import { GcmdService } from './gcmd.service';
import { FightEntity } from 'src/entities/fight.entity';
import { FightInfoEntity } from 'src/entities/fightInfo.entity';
import { FightGeneralEntity } from 'src/entities/fightGeneral.entity';
import { GeneralEntity } from 'src/entities/general.entity';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { SkillTemplateEntity } from 'src/entities/skillTemplate.entity';
import { SoldierEntity } from 'src/entities/soldier.entity';
import { GoodsService } from './goods.service';
import { FightGoodsEntity } from 'src/entities/fightGoods.entity';
import { PersonNpcEntity } from 'src/entities/personNpc.entity';
import * as fs from 'fs';
import { TasksType } from 'src/utils/types';
import { RedisService } from 'src/middleware/redis.service';
import { TeamRolesEntity } from 'src/entities/teamRoles.entity';
import { CommonService } from 'src/middleware/common.service';
import { MapsEntity } from 'src/entities/maps.entity';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { taskListConfig } from 'src/utils/config1';
import { OtherService } from './config/other.service';
import { ConfigEntity } from 'src/entities/config.entity';
import { UserConfigProvider } from 'src/common/user-config.provider';
@Injectable()
//战场相关服务
export class FightService implements OnModuleInit {
    private readonly size = 20
    private readonly serviceName = 'fight'
    private readonly taskJson: TasksType
    private userSetting:any
    constructor(
        private readonly commonService: CommonService,
        private readonly tasksService: TasksService,
        private readonly generalService: GeneralService,
        private readonly gcmdService: GcmdService,
        private readonly goodsService: GoodsService,
        private eventEmitter: EventEmitter2,
        private readonly redisService: RedisService,
        private readonly otherService: OtherService,
        private readonly userConfigProvider: UserConfigProvider,
        @InjectRepository(ManorEntity) private readonly manorEntity: Repository<ManorEntity>,
        @InjectRepository(ConfigEntity) private readonly configEntity: Repository<ConfigEntity>,
        @InjectRepository(MapsEntity) private readonly mapsEntity: Repository<MapsEntity>,
        @InjectRepository(TeamRolesEntity) private readonly teamRolesEntity: Repository<TeamRolesEntity>,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(ManorEventEntity) private readonly manorEventEntity: Repository<ManorEventEntity>,
        @InjectRepository(LogsEntity) private readonly logsEntity: Repository<LogsEntity>,
        @InjectRepository(FightEntity) private readonly fightEntity: Repository<FightEntity>,
        @InjectRepository(FightInfoEntity) private readonly fightInfoEntity: Repository<FightInfoEntity>,
        @InjectRepository(FightGeneralEntity) private readonly fightGeneralEntity: Repository<FightGeneralEntity>,
        @InjectRepository(FightGoodsEntity) private readonly fightGoodsEntity: Repository<FightGoodsEntity>,
        @InjectRepository(GeneralEntity) private readonly generalEntity: Repository<GeneralEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        @InjectRepository(SkillTemplateEntity) private readonly skillTemplateEntity: Repository<SkillTemplateEntity>,
        @InjectRepository(SoldierEntity) private readonly soldierEntity: Repository<SoldierEntity>,
        @InjectRepository(PersonNpcEntity) private readonly personNpcEntity: Repository<PersonNpcEntity>,
        @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
        private dataSource: DataSource,
    ) { 
        this.taskJson = JSON.parse(fs.readFileSync('./src/taskJson/main.json', 'utf8'));
        this.userSetting=this.userConfigProvider.getUserSetting()
    }
    async onModuleInit() {
        let fightList=await this.fightEntity.find({where:{status:1}})
        fightList.forEach(item => {
            this.createFightTimer(item)
        })
    }
    //战场 目标id 是否npc（1是2不是） fightingId战场id generalId武将id（如果没设置近身手动选择武将时使用）
    //这个是 玩家攻击npc  或者玩家攻击玩家
    /**
     * 如果没有战场id 则创建
     * 如果有攻击id 则战斗一次
     * 拿到攻击方武将列表  
     * 拿到防守方武将列表
     * 判断是不是本人的战场 显示其他菜单
     */
    /**
     * 
     * @param sid 
     * @param cmd 
     * @param userId 
     * @param param3   
     * gpsId,地图id
     * targetID,怪物id
     * isNpc=1,
     * fightingId,战场id
     * generalId,武将id
     * attackType 攻击类型 类型&普通攻击&技能id
     * skillGenId 设置技能的武将id
     * @returns 
     */
    async fighting(sid: string, cmd: number, userId: string,{gpsId=null,targetID=null,isNpc=1,fightingId=null,generalId=null,attackType=null,skillGenId=null}) {
        let msg=''
        //创建战场
        if(!fightingId){
            let generalList1=await this.generalService.getGeneralList(userId);
            let generalList=[]
            if(generalId){
                generalList=generalList1.filter(item=>item.id==generalId)
            }else{
                generalList=generalList1.filter(item=>item.near&&!item.fightStatus&&item.hpNow>0).sort((a,b)=>a.near-b.near)
            }
            if(!generalList.length){
                //手动选择武将
                return this.chooseGeneral(sid, cmd, userId,{targetID,isNpc,gpsId})
            }
            //防守方武将列表
            let defenseGeneralList=[]
            if(isNpc==1){
                //如果副本怪物 则缓存队伍
                let mapInfo=JSON.parse(await this.redisService.hget('mapInfo',targetID)||'{}')
                //把当前npc信息缓存
                let {keyName,keyValue : npcInfo}:any=await this.redisService.hgetByPattern('npmInfoTemp','*npc'+targetID+'user'+userId)
                let tempId=getRandomInt(1,10000)
                if(npcInfo&&(npcInfo.intervalTime==0&&!mapInfo.dungeonName)){
                    npcInfo=JSON.parse(npcInfo)
                    tempId=keyName
                    npcInfo.generals.forEach(item=>{
                        item.lastHurt = 0;
                        item.soldiers.forEach(soldier => {
                            soldier.lastDeadCount = 0;
                        });
                    })
                }else{
                    npcInfo=await this.gcmdService.getNpcInfo(targetID)
                    tempId+='npc'+npcInfo.id+'user'+userId
                }
                await this.redisService.hset('npmInfoTemp', tempId, JSON.stringify(npcInfo))
                npcInfo.generals.forEach(item=>{
                    defenseGeneralList.push({id:tempId+'gId'+item.id,name:item.name})
                })

                //处理怪物显示间隔
                if(npcInfo.intervalTime){
                    if(npcInfo.type==='p'){await this.redisService.setstr('p'+npcInfo.id+'u'+userId,userId,npcInfo.intervalTime)}
                    if(npcInfo.type==='a'){await this.redisService.setstr('a'+npcInfo.id,userId,npcInfo.intervalTime)}
                }
                if(mapInfo.dungeonName){
                    let teamRole=await this.teamRolesEntity.findOne({where:{userId:Number(userId)},relations:['team']})
                    await this.redisService.setstr('t'+npcInfo.id+'u'+String(teamRole.team.id),npcInfo.name)
                }
            }else{
                defenseGeneralList=await this.generalService.getGeneralList(targetID);
                defenseGeneralList=defenseGeneralList.filter(item=>item.near&&!item.fightStatus&&item.hpNow>0).sort((a,b)=>a.near-b.near)
            }
            fightingId=await this.createFight1({attackGeneralList:generalList,defenseGeneralList,gpsId,attackId:userId,defenseId:targetID})
            let routerList=JSON.parse(await this.redisService.hget('user'+userId,'routerList'));
            routerList[routerList.length-1].params.fightingId=fightingId
            let newRootList=[routerList.shift(),routerList.pop()]
            await this.redisService.hset('user'+userId,'routerList',JSON.stringify(newRootList))
            let fightEvent=await this.fightEntity.findOne({where:{id:fightingId}})
            //创建定时器
            await this.createFightTimer(fightEvent)
        }
        if(attackType){
            //1 技能 2逃跑 3使用物品  类型&名称&id
            let skillArr=attackType.split('&')
            let generalInfo=await this.generalService.getGeneralDetail(skillGenId)
            let skillInfo=generalInfo.skills.filter(item=>item.id==skillArr[2])[0]
            if(skillArr[0]==3){
                let usergood=await this.personGoodsEntity.findOne({where:{id:Number(skillArr[2])},relations:['good']})
                if(usergood){
                    msg=await this.goodsService.canUse('general',generalInfo.id,usergood.good.id,true)       
                }else{
                    msg=skillArr[1]+'不存在</br>'
                }
            }
            //使用除了普通攻击的技能，必须装备对应武器
            if(skillArr[0]==1&&skillInfo.weaponType!=0){
                let equipId=generalInfo.bingqiId
                let goodInfo=await this.personGoodsEntity.findOne({where:{id:Equal(equipId)},relations:['good']})
                if(!equipId||!goodInfo||(skillInfo.weaponType!=goodInfo.good.subType)){
                    msg=`${generalInfo.name}使用${skillArr[1]}必须装备${weaponType[skillInfo.weaponType]}</br>`
                }
            }
            if(!msg){
                //攻击
                await this.generalEntity.update({id:skillGenId},{attackMethod:attackType})
                await this.attack(fightingId)
                let fightEvent=await this.fightEntity.findOne({where:{id:fightingId}})
                await this.createFightTimer(fightEvent)
            }
        }
        //是不是自己的战场待处理 
        const params = {sid, cmd} 
        const urlObj = { [cmd]: { name: 'fighting', title: '战场',service:'fight', params:{fightingId} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let fightEvent=await this.fightEntity.findOne({where:{id:fightingId},relations:['fightGenerals']})
        let userSetting:any=JSON.parse(await this.redisService.hget('user'+userId,'userSetting'))
        let attackArr=[],defenceArr=[];//攻击方和防守方武将
        //传入武将列表 
        const handelGeneral=async (generalList,generalarr,type)=>{
            for (let index = 0; index < generalList.length; index++) {
                let item = generalList[index];
                if(item[type]?.includes('npc')){
                    let gId1=extractBetween(item[type],'gId','')
                    //是npc
                    let npmInfo=JSON.parse(await this.redisService.hget('npmInfoTemp',extractBetween(item[type],'','gId')))
                    if(!npmInfo){
                        let npcId=extractBetween(item[type],'npc','user')
                        npmInfo=await this.gcmdService.getNpcInfo(npcId)
                        await this.redisService.hset('npmInfoTemp',extractBetween(item[type],'','gId'),JSON.stringify(npmInfo))
                    }
                    npmInfo.generals.forEach(item1=>{
                        if(item1.id==gId1) generalarr.push(item1)  
                        
                    }) 
                }else{
                    let generalInfo=await this.generalService.getGeneralDetail(Number(item[type]))
                    generalarr.push(generalInfo) 
                } 
            }  
        }
        //拿到攻击方武将 
        let tempArr=fightEvent.fightGenerals.filter(item=>item.attackGeneralId&&item.isDead==1)
        
        await handelGeneral(tempArr,attackArr,'attackGeneralId')
        //拿到防守方武将
        tempArr=fightEvent.fightGenerals.filter(item=>item.defenseGeneralId&&item.isDead==1)
        await handelGeneral(tempArr,defenceArr,'defenseGeneralId')
        //判断战场是否结束  
        if(!attackArr.filter(item=>item.hpNow>0).length||!defenceArr.filter(item=>item.hpNow>0).length){
            this.tasksService.clearInterval('fight'+fightingId)
            let attackArr1=fightEvent.fightGenerals.filter(item=>item.attackGeneralId)
            let defenceArr1=fightEvent.fightGenerals.filter(item=>item.defenseGeneralId)
            let monsterArr=fightEvent.fightGenerals.filter(item=>item.defenseGeneralId?.includes('npc'))
            let status=2
            if(defenceArr.filter(item=>item.hpNow>0).length)status=3
            if(!attackArr.filter(item=>item.hpNow>0).length&&!defenceArr.filter(item=>item.hpNow>0).length)status=4
            if(monsterArr.length&&status==2){
                let fightInfoResult1=await this.fightInfoEntity.find({where:{
                    fightEvent:{id:fightingId},
                    harm:MoreThan(0),
                    generalId:In(attackArr1.map(item=>item.attackGeneralId))
                }})
                let fightInfoResult=[]
                fightInfoResult1.forEach(item=>{
                    let temp=fightInfoResult.find(item1=>item1.generalId==item.generalId)
                    if(temp){
                        temp.harm+=item.harm
                    }else{
                        fightInfoResult.push(item)
                    }

                })
                let totalHarm=fightInfoResult.reduce((a,b)=>a+b.harm*1,0)
                let npcId=monsterArr[0].defenseGeneralId
                npcId=extractBetween(npcId,'npc','user')
                let npcInfo=await this.gcmdService.getNpcInfo(Number(npcId))
                let level=npcInfo?.generals[0]?.level
                let potential=npcInfo.potential||level*100
                let gold=npcInfo.potential||level*3
                if(npcInfo.npcGoods.length){
                    //处理掉落物品
                    let randomNum=getRandomInt(0,99)
                    console.log('随机数',randomNum);
                    let goodsArr=[]
                    for (const element of npcInfo.npcGoods) {
                        if(randomNum>=element.min&&randomNum<=element.max){
                            goodsArr.push({
                                goodId:element.goodId,
                                count:element.count,
                                goodName:element.goodName,
                                fight:fightEvent
                            })
                        }
                    }
                    if(goodsArr.length){
                        if(this.userSetting.autoPick==1){
                            await this.autoPick(goodsArr)
                        }else{
                            await this.fightGoodsEntity.save(goodsArr)
                        }
                    }
                }

                //分配奖励
                let userArrObj=[]
                fightEvent.fightGenerals.forEach(item=>{
                    if(item.isDead==1){
                        let id=isPureNumber(item.attackGeneralId)?item.attackGeneralId:item.defenseGeneralId
                        let temp=fightInfoResult.filter(item1=>item1.generalId==id)
                        if(temp.length){
                            let userArr={id:item.userId,potential:0,gold:0,wood:0,stone:0,iron:0,food:0};
                            let i=userArrObj.findIndex(item1=>item1.id==item.userId)
                            if(i!=-1){
                                userArr=userArrObj[i]
                            }else{
                                userArrObj.push(userArr)
                                i=0
                            }
                            let prop=temp[0].harm/totalHarm
                            item.potential=Math.floor(potential*prop)
                            item.gold=Math.floor(gold*prop)
                            item.wood=Math.floor(npcInfo.wood*prop)
                            item.stone=Math.floor(npcInfo.stone*prop)
                            item.iron=Math.floor(npcInfo.iron*prop)
                            item.food=Math.floor(npcInfo.food*prop)

                            userArr.potential+=item.potential
                            userArr.gold+=item.gold
                            userArr.wood+=item.wood
                            userArr.stone+=item.stone
                            userArr.iron+=item.iron
                            userArr.food+=item.food
                            userArrObj[i]=userArr
                        }
                    }
                })
                await this.redisService.hdel('npmInfoTemp',extractBetween(monsterArr[0].defenseGeneralId,'','gId'));
                let arr=[];
                userArrObj.forEach(item=>{
                    // this.eventEmitter.emit('writeLogs', { userId:item.id, name: '打怪npcId:'+npcId,
                    //     gold:item.gold,
                    //     wood:item.wood,
                    //     stone:item.stone,
                    //     iron:item.iron,
                    //     food:item.food,
                    //     potential:item.potential,
                    //     reputation:item.reputation })
                    arr.push(this.roleEntity.update({id:item.id},{potential:()=>"potential + "+item.potential,gold:()=>"gold + "+item.gold,wood:()=>"wood + "+item.wood,stone:()=>"stone + "+item.stone,iron:()=>"iron + "+item.iron,food:()=>"food + "+item.food,prestige:()=>"prestige + "+npcInfo.reputation}))
                })
                if(userArrObj.find(item=>item.id==fightEvent.attackId)){
                    let msg=await this.handelTaskNpc(fightEvent.attackId,npcId)
                    fightEvent.taskUserId=fightEvent.attackId
                    fightEvent.remark=msg
                }
                await Promise.all(arr)
            }
            if(monsterArr.length&&(status==3||status==4)){
                //战斗失败释放怪物
                // 3143npc8user148gId2
                let npcId=monsterArr[0].defenseGeneralId
                npcId=extractBetween(npcId,'npc','user')
                let npcInfo=await this.gcmdService.getNpcInfo(Number(npcId))
                //如果副本怪物 则缓存队伍
                let mapInfo=JSON.parse(await this.redisService.hget('mapInfo',npcInfo.gpsId)||'{}')
                if(mapInfo.dungeonName){
                    let teamRole=await this.teamRolesEntity.findOne({where:{userId:Number(fightEvent.attackId)},relations:['team']})
                    await this.redisService.delstr('t'+npcInfo.id+'u'+String(teamRole?.team.id))
                }else{
                    if(npcInfo.intervalTime){
                        if(npcInfo.type==='p'){await this.redisService.delstr('p'+npcInfo.id+'u'+userId)}
                        if(npcInfo.type==='a'){await this.redisService.delstr('a'+npcInfo.id)}
                    }
                }
            }
            fightEvent.attackGeneralStr=attackArr1.map(item=>item.name+'部').join(',')
            fightEvent.defenseGeneralStr=defenceArr1.map(item=>item.name+'部').join(',')
            fightEvent.status=status
            await this.fightEntity.save(fightEvent)
            await this.redisService.hdel('gpsfight',String(fightEvent.gpsId));
            //清理参战双方缓存
            let generalArr=fightEvent.fightGenerals.filter(item=>{
                let gId=item.attackGeneralId?.includes('npc')?item.attackGeneralId:item.defenseGeneralId
                status==2&&(this.redisService.hdel('npmInfoTemp',extractBetween(gId,'','user')))
                return isPureNumber(item.attackGeneralId)||isPureNumber(item.defenseGeneralId)
            })
            let garr=[]
            for (let index = 0; index < generalArr.length; index++) {
                const element = generalArr[index];
                let gId=Number(isPureNumber(element.attackGeneralId)?element.attackGeneralId:element.defenseGeneralId);
                garr.push(gId)
            }
            const generals = await this.generalEntity.find({
                where: {
                    id: In(garr),
                },
                relations: ['soldiers'],  // 如果你需要一起加载 soldiers 关系
            });
            let fightInfo=[]
            generals.forEach(general => {
                general.lastHurt = 0;
                general.fightStatus = 0;
                general.soldiers.forEach(soldier => {
                    soldier.lastHurt = 0;
                    if(status!=2){
                        soldier.count=Math.floor(soldier.count*0.7)
                        fightInfo.push({
                            desc:`${general.name}部${soldier.name}逃亡${soldier.count}名`,
                            fightEvent:{id:fightingId},
                        })
                    }
                });
            });
            await this.fightInfoEntity.save(fightInfo)
            await this.generalEntity.save(generals);
            generals.forEach(item=>{
                this.commonService.generalPopulation(item.id)
            })
            //清理地图战场缓存
            this.redisService.hdel('gpsfight',String(fightEvent.gpsId));
            //处理副本怪物缓存
            if(monsterArr.length&&status==2){
                let npcId=monsterArr[0].defenseGeneralId
                npcId=extractBetween(npcId,'npc','user')
                let npcInfo=await this.gcmdService.getNpcInfo(Number(npcId))
                if(npcInfo.dungeonName){
                    msg='你成功闯过'+npcInfo.dungeonName+'副本'
                    this.eventEmitter.emit('sendMsgToRole', {type:3,content:`完成${npcInfo.dungeonName}副本，自动退出副本`,userId:userId })
                    this.eventEmitter.emit('clearDungeon',userId,npcInfo.dungeonName)
                }
            }
            //清除
            return await this.fightEnd(sid, cmd, userId,{fightingId:fightEvent.id,msg})
        }

        //传入武将列表拿到详情
        const handelGeneralInfo=async (generalList)=>{
            let myGeneralStr=''
            for(let i=0;i<generalList.length;i++){
                let item=generalList[i]
                let flag=''
                if(item.userId){
                    userSetting=JSON.parse(await this.redisService.hget('user'+item.userId,'userSetting')||'{}')
                    flag='['+userSetting.flag+']'
                }
                let str11=''//拿到士兵
                item.soldiers.forEach((item1,index1) => {
                    if(item1.count>0){
                        str11+=`${item1.name}(${item1.count})${item1.lastDeadCount?' - '+item1.lastDeadCount:''}<br/>`
                    }
                })
                let symbol=''
                if(item.lastHurt){
                    symbol=' - '+item.lastHurt
                }
                if(item.lastHurt<0){
                    symbol=' + '+Math.abs(item.lastHurt)
                }
                myGeneralStr+=`*<a href="${this.seturlOther(params,urlObj,'武将详情','armyGeneral',{generalId:item.id},'general')}">${item.name}部${flag}</a>:<br/>
                ${item.name}(${item.hpNow}/${item.hp})${symbol}<br/>
                ${str11}`
            }
            return myGeneralStr
        }
        let myGeneralStr=await handelGeneralInfo(attackArr);
        let targetGeneralStr=await handelGeneralInfo(defenceArr);
        let controllGeneral=null
        if(!controllGeneral)controllGeneral=attackArr[0]
        const kuaijiefn=async (key)=>{
            let arr=controllGeneral['cmd'+key].split('&')
            //如果是使用物品 判断是不是存在
            if(arr[0]==3){
                let goods=await this.personGoodsEntity.findOne({where:{id:Number(arr[2])}})
                if(!goods||goods.count<1){
                    arr=[]
                }
            }
            if(arr.length>1){
                return `<a href="${this.seturlOther(params,urlObj,'战场','fighting',{fightingId,attackType:controllGeneral['cmd'+key],skillGenId:controllGeneral.id})}">${controllGeneral['cmd'+key].split('&')[1]}</a> `
            }else{
                return `<a href="${this.seturlOther(params,urlObj,'快捷键','fightMiddle',{keyId:key,generalId:controllGeneral.id})}">快捷</a> `
            }
        }
        let futureTime=new Date(fightEvent.updateDate).getTime()+fightEvent.interval
        let content = `${msg}下回合还有${formatDuring(futureTime - new Date().getTime())} <br/>
        ${await kuaijiefn(1)}
        ${await kuaijiefn(2)}
        ${await kuaijiefn(3)}<br/>
        ${myGeneralStr}
        ------<br/>
        ${targetGeneralStr}
        ${await kuaijiefn(4)}
        ${await kuaijiefn(5)}
        ${await kuaijiefn(6)}<br/>
        <a href="${this.seturlOther(params,urlObj,'战场','fighting',{targetID,isNpc,fightingId,})}">刷新</a><br/>
        <a href="${this.seturlOther(params,urlObj,'战场详情','fightInfo',{fightingId})}">查看详情</a><br/>
        加入战场<a href="">攻方</a><br/>
        <a href="">允许他人加入战场</a><br/>
        <a href="${this.seturlOther(params,urlObj,'快捷键设置','fightsetKey',{generalId:controllGeneral.id})}">快捷键设置</a><br/>
        ${backRouter}`
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content; 
    }
    //处理攻击逻辑
    async attack(fightingId){
        // 先考虑一种情况 攻击方是玩家 防守方是npc
        let fightEvent=await this.fightEntity.findOne({where:{id:fightingId},relations:['fightGenerals']});
        let deadGeneralIdArr=[];//死掉的武将
        //拿到所有武将
        let attackArr=fightEvent.fightGenerals.filter(item=>item.isDead==1&&item.attackGeneralId)
        let defenceArr=fightEvent.fightGenerals.filter(item=>item.isDead==1&&item.defenseGeneralId)
        if(!attackArr.length||!defenceArr.length){
            //如果战斗结束 则清除缓存
            this.fighting('',0,'',{fightingId})
            this.tasksService.clearInterval('fight'+fightingId)
            this.eventEmitter.emit('sendMsgToRole', {type:3,content:`${fightEvent.name}战斗已结束`,userId:fightEvent.attackId })
            return
        }
        let defenceArrCopy=JSON.parse(JSON.stringify(defenceArr))
        //玩家武将实体 和npc武将实体
        let playGeneralEntityArr=[],npcGeneralEntityArr=[];
        //攻击相关的说明  a 打 b  b 再打 a
        //如果没有设置攻击目标 那就默认打第一个武将
        const handelHp=async (str:string)=>{
            if(str){
                let arr=str.split('&')
                if(arr[0]=='3'){
                    let goods=await this.personGoodsEntity.findOne({where:{id:Number(arr[2])},relations:['good']})
                    return goods.good.addhp
                }
            }
            return 0
        }
        //攻击-防御
        let isPlayer=false
        for (let index = 0; index < attackArr.length; index++) {
            let element = attackArr[index];
            let gId=element.attackGeneralId
            let attackInfo:GeneralEntity
            if(element.attackGeneralId.includes('npc')){
                gId=extractBetween(element.attackGeneralId,'','gId')
                let gId1=extractBetween(element.attackGeneralId,'gId','')
                attackInfo=JSON.parse(await this.redisService.hget('npmInfoTemp',String(gId))).generals.find(item=>item.id==gId1)
            }else{
                attackInfo=await this.generalService.getGeneralDetail(Number(gId))
                isPlayer=true
            }
            
            let defenceInfo:GeneralEntity;
            let dId=element.targetGeneralId?element.targetGeneralId:String(defenceArr[0].defenseGeneralId)
            //剩余血量
            let remainHp=0
            if(dId.includes('npc')){
                let gId1=extractBetween(dId,'gId','')
                dId=extractBetween(dId,'','gId')
                let npcInfo=JSON.parse(await this.redisService.hget('npmInfoTemp',String(dId)))
                if(!npcInfo){
                    let npcInfo=await this.gcmdService.getNpcInfo(Number(dId))
                    await this.redisService.hset('npmInfoTemp',String(dId),JSON.stringify(npcInfo))
                }
                defenceInfo=npcInfo.generals.find(item=>item.id==gId1)
                if(npcGeneralEntityArr.findIndex(item=>item.id==dId)==-1){
                    npcGeneralEntityArr.push({id:dId,npcInfo})
                }
            }else{
                defenceInfo=await this.generalService.getGeneralDetail(Number(dId))
                remainHp=await handelHp(defenceInfo.attackMethod)
                playGeneralEntityArr.push(defenceInfo)
            }
            await this.attackProcess(attackInfo,defenceInfo,fightingId,isPlayer)
            
            defenceInfo.soldiers.forEach(item=>{
                let allHarm=item.lastHurt+item.lastDamage
                if(remainHp>allHarm){
                    remainHp-=allHarm
                    allHarm=0
                }else{
                    allHarm-=remainHp
                    remainHp=0
                }
                let count=Math.floor((allHarm)/item.hp)
                count>item.count&&(count=item.count)
                item.count=item.count-count
                item.lastDeadCount=count
                item.lastDamage=allHarm%item.hp
            })
            defenceInfo.hpNow-=(defenceInfo.lastHurt-remainHp)
            if(defenceInfo.hpNow<0){
                let id=defenceArr.find(item=>item.defenseGeneralId.includes(dId)).id
                defenceInfo.hpNow=0
                deadGeneralIdArr.push(id)
            }
            defenceInfo.lastHurt=defenceInfo.lastHurt-remainHp
            if(dId.includes('npc')){
                let gId1=extractBetween(dId,'gId','')
                dId=extractBetween(dId,'','gId')
                await this.redisService.hset('npmInfoTemp',dId,JSON.stringify(defenceInfo))
            }
        }
        //防御->攻击
        isPlayer=false
        for (let index = 0; index < defenceArrCopy.length; index++) {
            //剩余血量
            let remainHp=0
            let element = defenceArrCopy[index];
            let gId=element.defenseGeneralId
            let attackInfo:GeneralEntity
            if(element.defenseGeneralId.includes('npc')){
                gId=extractBetween(element.defenseGeneralId,'','gId')
                let gId1=extractBetween(element.defenseGeneralId,'gId','')
                attackInfo=JSON.parse(await this.redisService.hget('npmInfoTemp',String(gId))).generals.find(item=>item.id==gId1)
            }else{
                attackInfo=await this.generalService.getGeneralDetail(Number(gId))
                isPlayer=true
            }
            
            let defenceInfo:GeneralEntity;
            let dId=element.targetGeneralId?element.targetGeneralId:String(attackArr[0].attackGeneralId)
            if(dId.includes('npc')){
                let gId1=extractBetween(dId,'gId','')
                dId=extractBetween(dId,'','gId')
                let npcInfo=JSON.parse(await this.redisService.hget('npmInfoTemp',String(dId)))
                defenceInfo=npcInfo.generals.find(item=>item.id==gId1)
                npcGeneralEntityArr.push({id:dId,npcInfo})
            }else{
                defenceInfo=await this.generalService.getGeneralDetail(Number(dId))
                remainHp=await handelHp(defenceInfo.attackMethod)
                defenceInfo.attackMethod=''
                playGeneralEntityArr.push(defenceInfo)
            }
            await this.attackProcess(attackInfo,defenceInfo,fightingId,isPlayer)
            defenceInfo.soldiers.forEach(item=>{
                let allHarm=item.lastHurt+item.lastDamage
                if(remainHp>allHarm){
                    remainHp-=allHarm
                    allHarm=0
                }else{
                    allHarm-=remainHp
                    remainHp=0
                }
                let count=Math.floor((allHarm)/item.hp)
                count>item.count&&(count=item.count)
                item.count-=count
                item.lastDeadCount=count
                item.lastDamage=allHarm%item.hp
            })
            defenceInfo.hpNow-=(defenceInfo.lastHurt-remainHp)
            if(defenceInfo.hpNow<0){
                let id=attackArr.find(item=>item.attackGeneralId.includes(dId)).id
                deadGeneralIdArr.push(id)
                defenceInfo.hpNow=0
            }
            defenceInfo.lastHurt=defenceInfo.lastHurt-remainHp
        }
        if(playGeneralEntityArr.length){
            playGeneralEntityArr.forEach(item=>{
                this.commonService.generalPopulation(item.id)
            })
            await this.generalEntity.save(playGeneralEntityArr)
        }
        for (let index = 0; index < npcGeneralEntityArr.length; index++) {
            const element = npcGeneralEntityArr[index];
            await this.redisService.hset('npmInfoTemp',String(element.id),JSON.stringify(element.npcInfo))
        }
        await this.fightGeneralEntity.update({id:In(deadGeneralIdArr)},{isDead:2})
        await this.fightEntity.update({id:fightingId},{updateDate:new Date()})
    }
    //攻击过程  
    async attackProcess(attackInfo,defenceInfo,fightingId,isPlayer=false){
        //武将、士兵、装备、技能 四个都要有
        let fightInfo=[]
        let attackSoldiers=attackInfo.soldiers.filter(item=>item.count&&item.teamOrder<5)
        let defenceSoldiers=defenceInfo.soldiers.filter(item=>item.count)
        //先根据梯队排序 在这基础上根据士兵数量排序
        const sort1=(arr)=>{
            arr.sort((a,b)=>{
                if(a.teamOrder!=b.teamOrder){
                    return a.teamOrder-b.teamOrder
                }else{
                    return b.count*a.population-a.count*b.population
                }
            })
            return arr
        }
        let attackTeamOrderArr=[],defenceTeamOrderArr=[]
        if(attackSoldiers.length){
            attackSoldiers = sort1(attackSoldiers)
            attackTeamOrderArr = [...new Set(attackSoldiers.map(item => item.teamOrder))]
        }
        if(defenceSoldiers.length){
            defenceSoldiers = sort1(defenceSoldiers)
            defenceTeamOrderArr = [...new Set(defenceSoldiers.map(item => item.teamOrder))]
            defenceSoldiers.forEach(item => {
                item.lastHurt=0
            });
        }
        defenceInfo.lastHurt=0
        //不存在就攻击武将
        let attcackTarget=defenceSoldiers[0]??defenceInfo
        //武将和士兵同一梯队 或者武将比士兵梯队靠前 也是攻击武将
        if(defenceInfo.teamOrder<=attcackTarget.teamOrder)attcackTarget=defenceInfo
        let allHarm=0//总伤害
        const damageRatio=(order)=>{
            //梯队递减  每层递减0.15
            return 1-attackTeamOrderArr.indexOf(order)*0.15
        }
        //武将技能开始
        let skill:any=''
        // 武将攻击 类型&名称&id
        if(attackInfo.skillStr){
            //怪物技能
            skill=await this.getSkillTemplate(attackInfo.skillStr)
            if(!skill){
                skill=await this.getSkillTemplate('大刀砍术')
            }
        }else{
            skill=attackInfo.skills.filter(item=>item.isDefault)[0]
            if(attackInfo.attackMethod){
                //处理使用技能或物品
                let skillArr=attackInfo.attackMethod.split('&')
                //技能
                skill=skillArr[0]
                if(skillArr[0]==1){
                    skill=attackInfo.skills.filter(item=>item.id==Number(skillArr[2]))[0]
                }
            }else{
                let equipId=attackInfo.bingqiId
                let goodInfo=await this.personGoodsEntity.findOne({where:{id:Equal(equipId)},relations:['good']})
                //技能要求的武器不一样 默认使用普通攻击
                if(!equipId||!skill||(skill.weaponType!=0&&skill.weaponType!=goodInfo.good.type))skill=attackInfo.skills.filter(item=>item.name=='普通攻击')[0]
            }
            
        }
        //武将技能结束
        
        let jumpAttackBool=false// 兵全部被打死就攻击武将
        for (let index = 0; index < attackSoldiers.length; index++) {
            let soldier = attackSoldiers[index];
            let skill:any='普通攻击'
            if(soldier.skillStr){
                skill=await this.getSkillTemplate(soldier.skillStr||'普通攻击')
                if(!skill){
                    skill=await this.getSkillTemplate('普通攻击')
                }
            }else{
                skill=soldier.skills?.filter(item=>item.isDefault)[0]
                if(!skill||(skill.weaponType!=0&&skill.weaponType!=soldier.equip.weaponType)){
                    skill=soldier?.skills.filter(item=>item.name=='普通攻击')[0]
                }
            }
            //伤害
            let harm=Math.floor(soldier.attack*(getRandomNumber(1.1,1.3)+soldier.count/20)+skill.damage*20*damageRatio(soldier.teamOrder))
            if(harm<attcackTarget.defense)harm=0//不破防就是0
            //如果是士兵 就乘以伤害数量
            if(!attcackTarget.countAll)harm*=skill.damageNum
            //减去防御
            harm-=attcackTarget.defense
            if(harm<0)harm=0//不破防就是0
            allHarm+=harm
            attcackTarget.lastHurt+=harm
            if(attcackTarget.count <= Math.ceil(allHarm / attcackTarget.hp)){
                jumpAttackBool=true
            }
            console.log(`${attackInfo.name}部的${soldier.name}使用${skill.name}攻击${defenceInfo.name}部的${attcackTarget.name}，产生${harm}伤害`);
            fightInfo.push({
                desc:`${attackInfo.name}部的${soldier.name}使用${skill.name}攻击${defenceInfo.name}部的${attcackTarget.name}，产生${harm}伤害`,
                fightEvent:{id:fightingId},
                harm:harm,
                generalId:attackInfo.id,
            })
        }
        
        //如果兵被打完了 就判断技能攻击距离  攻击最后梯队的武将 
        let harm=Math.floor(attackInfo.attack*getRandomNumber(1.5,1.9)+skill.damage*20)
        if(skill==3){
            let skillArr=attackInfo.attackMethod.split('&')
            let goodInfo=await this.personGoodsEntity.findOne({where:{id:Number(skillArr[2])},relations:['good']})
            let msg=''
            if(goodInfo.good.type==1){
                msg=`,抵消了${goodInfo.good.addhp}伤害`
            }else{
                //使用物品其他逻辑
            }
            await this.personGoodsEntity.update({id:Number(skillArr[2])},{count:goodInfo.count-1})
            await this.goodsService.updateUserWeight(attackInfo.userId)
            fightInfo.push({
                desc:`${attackInfo.name}部的${attackInfo.name}使用物品${skillArr[1]}${msg}`,
                fightEvent:{id:fightingId},
                harm:0,
                generalId:0,
            })
        }else if(skill==2){
            //逃跑
            let msg=Math.random()*3<1?`逃跑成功`:`逃跑失败`
            fightInfo.push({
                desc:`${attackInfo.name}部的${attackInfo.name}使用逃跑技能，${msg}`,
                fightEvent:{id:fightingId},
                harm:0,
                generalId:0,
            })
        }else{
            defenceSoldiers=defenceSoldiers.filter(item=>item.count>0)
            let target=this.jumpAttack(jumpAttackBool,defenceInfo.teamOrder,skill.distance,skill.damageNum,defenceSoldiers,harm)
            if(target){
                //攻击士兵
                defenceInfo.soldiers.forEach(element => {
                    if(element.id==target.id){
                        harm-=element.defense 
                        if(harm<0)harm=0//不破防就是0
                        harm*=skill.damageNum
                        // element.hp
                        element.lastHurt+=harm
                    }
                });
            }else{
                target=defenceInfo
                //攻击武将
                harm-=defenceInfo.defense 
                if(harm<0)harm=0
                defenceInfo.lastHurt+=harm
            }
            console.log(`${attackInfo.name}部的${attackInfo.name}使用${skill.name}攻击${defenceInfo.name}部的${target.name}，产生${harm}伤害`);
            fightInfo.push({
                desc:`${attackInfo.name}部的${attackInfo.name}使用${skill.name}攻击${defenceInfo.name}部的${target.name}，产生${harm}伤害`,
                fightEvent:{id:fightingId},
                harm:harm,
                generalId:attackInfo.id,
            })
        }
        
        this.fightInfoEntity.save(fightInfo) 
    }
    //武将跳跃攻击 筛选要攻击的目标
    /**
     * 
     * @param generalOrder 武将所在梯队
     * @param distance 技能攻击距离
     * @param damageNum 技能攻击数量
     * @param defenceSoldiers 士兵列表
     * @param harm 伤害
     */
    jumpAttack(jumpAttackBool,generalOrder,distance,damageNum,defenceSoldiers,harm){
        //拿到最多能攻击到的梯队
        let defenceTeamOrderArr=[...new Set(defenceSoldiers.map(item=>item.teamOrder))]
        let targetTeam=defenceTeamOrderArr[distance]??defenceTeamOrderArr[defenceTeamOrderArr.length-1]//能攻击到梯队
        //同时满足 小于武将梯队 并且小于等于目标梯队
        defenceSoldiers=defenceSoldiers.filter(item=>{
            return item.teamOrder<generalOrder&&item.teamOrder<=targetTeam
        })
        //如果士兵能防住 就攻击士兵 并返回士兵
        if(defenceSoldiers.length){
            defenceSoldiers=defenceSoldiers.sort((a,b)=>b.count*a.population-a.count*b.population)
            if(!jumpAttackBool){
                return defenceSoldiers[0]
            }
            for (let index = 0; index < defenceSoldiers.length; index++) {
                const element = defenceSoldiers[index];
                if(element.count*element.hp>=harm*damageNum){
                    return element
                }
            }
        }
        
    }
    //创建战场 generalList攻击方武将列表  targetList防守方武将列表
    //
    async createFight(userId,targetID,isNpc,generalList){
        const userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: userId });
        let targetList=isNpc==1?((await this.gcmdService.getNpcInfo(targetID)).generals):await this.generalService.getGeneralList(targetID)
        let fighttId:number;
        if(isNpc==1){
            let npcInfo=await this.gcmdService.getNpcInfo(targetID)
            targetList=npcInfo.generals
            targetID=String(targetID)+getRandomInt(1,10000)
            targetID=targetID+'npc'+npcInfo.id+'user'+userId
            //把当前npc信息缓存
            await this.redisService.hset('npmInfoTemp', targetID, JSON.stringify(npcInfo))
        }
        await this.dataSource.transaction(async (manager) => {
            // 创建战场
            let fight = new FightEntity();
            fight.gpsId = userInfo.gpsId;
            fight.name = `${generalList[0].name}部 VS ${targetList[0].name}部`;
            fight.attackId = userId;
            fight.defenseId = targetID;
            fight.attackIsNpc = 2;
            fight.defenseIsNpc=isNpc
            fight.fightInfos = [];
            fight.fightGenerals = [];
    
            // 战斗详情
            let fightInfo = new FightInfoEntity();
            fightInfo.fightEvent = fight;
            fightInfo.desc = `${generalList[0].name}部向${targetList[0].name}部发起攻击，双方展开了激烈的战斗...`;
            fight.fightInfos.push(fightInfo);
    
            // 战斗武将 - 攻击方
            generalList.forEach(item => {
                let fightGeneral = new FightGeneralEntity();
                fightGeneral.attackGeneralId = item.id;
                fightGeneral.name = item.name;
                fightGeneral.fightEvent = fight;
                fight.fightGenerals.push(fightGeneral);
            });
            // 战斗武将 - 防御方 npc就不要插入了
            if(isNpc==2){
                targetList.forEach(item => {
                    let fightGeneral = new FightGeneralEntity();
                    fightGeneral.defenseGeneralId = item.id;
                    fightGeneral.name = item.name;
                    fightGeneral.fightEvent = fight;
                    fightGeneral.userId=userId
                    fight.fightGenerals.push(fightGeneral);
                });
            }
            // 保存战场及其关联对象
            await manager.save(fight);
            await manager.save(fight.fightGenerals);
            // 返回战场 ID
            fighttId=fight.id;
        });
        await this.redisService.hdel('gpsfight',String(userInfo.gpsId))
        return fighttId
    }
//攻击方和防守方武将列表
    async createFight1({attackGeneralList,defenseGeneralList,gpsId,attackId,defenseId}){
        let fighttId:number;
        let userName=''
        if(isPureNumber(attackId)){
            let userInfo=JSON.parse(await this.redisService.hget('user' + attackId, 'userInfo'))
            userName=userInfo.name
        }
        await this.dataSource.transaction(async (manager) => {
            // 创建战场
            let fight = new FightEntity();
            fight.gpsId = gpsId;
            fight.name = `${attackGeneralList[0].name}部 VS ${defenseGeneralList[0].name}部`;
            fight.attackId=attackId
            fight.fightInfos = [];
            fight.fightGenerals = [];
            // 战斗详情
            let fightInfo = new FightInfoEntity();
            fightInfo.fightEvent = fight;
            fightInfo.desc = `${attackGeneralList[0].name}部向${defenseGeneralList[0].name}部发起攻击，双方展开了激烈的战斗...`;
            fight.fightInfos.push(fightInfo);
            let generalArr=[]
            // 战斗武将 - 攻击方
            attackGeneralList.forEach(item => {
                let fightGeneral = new FightGeneralEntity();
                fightGeneral.attackGeneralId = item.id;
                fightGeneral.name = item.name;
                fightGeneral.fightEvent = fight;
                fightGeneral.userId=isPureNumber(item.id)?attackId:0
                fightGeneral.userName=isPureNumber(item.id)?userName:''
                fight.fightGenerals.push(fightGeneral);
                if(isPureNumber(item.id)){
                    let general = new GeneralEntity();
                    general.id = item.id;
                    general.fightStatus=1
                    generalArr.push(general)
                }
            });
            // 战斗武将 - 防御方 
            defenseGeneralList.forEach(item => {
                let fightGeneral = new FightGeneralEntity();
                fightGeneral.defenseGeneralId = item.id;
                fightGeneral.name = item.name;
                fightGeneral.fightEvent = fight;
                fightGeneral.userId=isPureNumber(item.id)?defenseId:0
                fight.fightGenerals.push(fightGeneral);
                if(isPureNumber(item.id)){
                    let general = new GeneralEntity();
                    general.id = item.id;
                    general.fightStatus=1
                    generalArr.push(general)
                }
            });
            // 保存战场及其关联对象
            await manager.save(fight);
            await manager.save(generalArr);
            // await manager.save(fight.fightGenerals);
            // 返回战场 ID
            fighttId=fight.id;
        });
        await this.redisService.hdel('gpsfight',gpsId)
        return fighttId
    }
    //战斗结束
    async fightEnd(sid: string, cmd: number, userId: string,{fightingId,goodName='',msg=''}){
        let fightEvent=await this.fightEntity.findOne({where:{id:fightingId},relations:['fightGenerals','fightGoods']})
        //拾取物品
        if(goodName){
            let goodInfo=fightEvent.fightGoods.find(item=>item.goodName==goodName)
            if(goodInfo&&goodInfo.status==1){
                let lockKey='fightGodd'+goodInfo.id
                const isLocked = await this.redisService.setnx(lockKey+userId);
                let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: Number(userId) });
                if(isLocked){
                    await this.goodsService.changePersonGoodByName(Number(userId), goodName, goodInfo.count,'add')
                    await this.fightGoodsEntity.update({id:goodInfo.id},{status:2})
                    msg=`捡到${goodName}x${goodInfo.count}`
                    let desc=`${userInfo.name}拾取物品${goodName}x${goodInfo.count}`
                    this.fightInfoEntity.insert({desc,fightEvent:{id:fightingId}})
                    //解锁
                    await this.redisService.del(lockKey)
                    fightEvent=await this.fightEntity.findOne({where:{id:fightingId},relations:['fightGenerals','fightGoods']})
                }else{
                    msg='拾取失败,物品不存在或已拾取' 
                }
                
                //写入日志
                this.eventEmitter.emit('writeLogs', { userId, name: '拾取物品:' + goodName+'x'+goodInfo.count+'战场id'+fightingId})
            }else{
                msg='拾取失败,物品不存在或已拾取'
            }
        }
        let contentStr=''
        if(fightEvent.status==3){
            let fightInfo=await this.fightInfoEntity.find({where:{fightEvent:{id:fightingId},generalId:IsNull()}})
            fightInfo.forEach(item=>{
                if(item.desc.includes('逃亡')){
                    contentStr+=`${item.desc}<br/>`
                }
            })
        }
        //计算战斗结果
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'fightEnd', title: '战场结束',service:'fight', params:{fightingId} } }
        fightEvent.fightGenerals.forEach((item) => {
            if(item.potential||item.gold||item.wood||item.food||item.stone||item.iron){
                contentStr+=`${item.name}为${item.userName}打到
                ${item.potential?'潜能'+item.potential+',':''}
                ${item.gold?'银两'+item.gold+',':''}
                ${item.wood?'木材'+item.wood+',':''}
                ${item.food?'粮草'+item.food+',':''}
                ${item.stone?'石料'+item.stone+',':''}
                ${item.iron?'生铁'+item.iron+',':''}</br>`
            }
        })
        let goodsStr=''
        for (const element of fightEvent.fightGoods) {
            if(element.status==1){
                goodsStr+=`<a href="${this.seturlOther(params,urlObj,'战场结束','fightEnd',{fightingId,goodName:element.goodName})}">${element.goodName}x${element.count}</a> `
            }else{
                goodsStr+=`${element.goodName}x${element.count}(已拾取) `
            }
        }
        if(goodsStr){
            goodsStr=`掉落了物品：${goodsStr}<br/>`
        }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content=`${msg?msg+'<br/>':''}战斗已经结束!<br/>
        ${fightEvent.status==2?'攻方胜利':'攻方战败'}(${fightEvent.attackGeneralStr})<br/>
        ${fightEvent.status==3?'守方胜利':'守方战败'}(${fightEvent.defenseGeneralStr})<br/>
        ${contentStr}
        ${goodsStr}
        <a href="${this.seturlOther(params,urlObj,'战场详情','fightInfo',{fightingId})}">查看详情</a><br/>${fightEvent.taskUserId==Number(userId)?fightEvent.remark:''}${backRouter}`
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content
    }
    //战场详情
    async fightInfo(sid: string, cmd: number, userId: string,{fightingId,page=1}){
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'fightInfo', title: '战场详情',service:'fight', params:{fightingId,page} } }
        let [InfoList, totalItems] = await this.fightInfoEntity.findAndCount({
            where: { fightEvent:{id:fightingId}  },
            order: { createDate: 'DESC' },
            take: this.size, skip: (page - 1) * this.size
        })
        let contentStr='';
        InfoList.forEach((item,index) => {
            //拿到item.createDate的时分
            let date = new Date(item.createDate)
            let time = date.getHours() + ':' + date.getMinutes()
            contentStr+=`[${time}]${item.desc}</br>`
        })
        //分页
        let pageStr = this.setpage(params, urlObj, page, totalItems, {fightingId}, 'fightInfo', '战场详情')
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `
        ${contentStr}
        ${pageStr}
        <a href="${this.seturlOther(params,urlObj,'战场详情','fightInfo',{fightingId,page})}">刷新</a><br/>
        ${backRouter}
        `
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content
    }
    // 战场快捷键设置
    async fightsetKey(sid: string, cmd: number, userId: string,{generalId,thingObj,keyId}) {
        if(thingObj?.id){
            let updateData = {};
            updateData[`cmd${keyId}`] = `3&${thingObj.name}&${thingObj.id}`;
            await this.generalEntity.update({id:generalId},updateData)
        } 
        let genertalInfo=await this.generalService.getGeneralDetail(generalId)
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'fightsetKey', title: '快捷键设置',service:'fight', params:{generalId} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let str=''
        for (let index = 1; index < 7; index++) {
            let cmd=`cmd${index}`
            str+=`快捷键${index}:<a href="${this.seturlOther(params,urlObj,'战场','fightMiddle',{keyId:index,generalId})}">${(genertalInfo[cmd].includes('&'))?(genertalInfo[cmd].split('&')[1]):genertalInfo[cmd]}</a><br/>`
        }
        let content = `${str}
        ${backRouter}`
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //设置技能中间页 选择药品 、逃跑、技能
    async fightMiddle(sid: string, cmd: number, userId: string,{keyId,generalId,skillId,taopao}) {
        let genertalInfo=await this.generalService.getGeneralDetail(generalId)
        // 类型&名称&id 1 技能 2逃跑 3使用物品
        if(taopao){
            let updateData = {};
            updateData[`cmd${keyId}`] = `2&逃跑`;
            await this.generalEntity.update({id:generalId},updateData)
        }
        if(skillId){
            let skillName=genertalInfo.skills.filter(item=>item.id==skillId)[0].name
            let updateData = {};
            updateData[`cmd${keyId}`] = `1&${skillName}&${skillId}`;
            await this.generalEntity.update({id:generalId},updateData)
        }
        if(skillId||taopao){
            //返回到上一级
            let routerInfo=await this.getPrevRouter(userId)
            return this[routerInfo.name](sid,cmd, userId,routerInfo.params)
        }
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'fightMiddle', title: '快捷键',service:'fight', params:{keyId,generalId} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let skillStr=''
        genertalInfo.skills.forEach((item) => {
            skillStr+=`<a href="${this.seturlOther(params,urlObj,'快捷键','fightMiddle',{keyId,generalId,skillId:item.id})}">${item.name}</a><br/>`
        })
        let content = `<a href="${this.seturlOther(params,urlObj,'快捷键','fightsetItem',{keyId,generalId})}">选择物品</a><br/>
        <a href="${this.seturlOther(params,urlObj,'快捷键','fightMiddle',{keyId,generalId,taopao:1})}">逃跑</a><br/>
        ${skillStr}
        ${backRouter}`
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content;
    }

    //设置物品快捷键
    async fightsetItem(sid: string, cmd: number, userId: string,{keyId,generalId,thindId,page=1,type,subType}) {
        
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'useGoods', title: '使用物品',service:'useGoods', params:{keyId,generalId,page} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let typestr='',subTypestr='';//主分类和子分类
        goodsType.forEach((item,index) => {
            typestr+=`${type==item.id?`${item.name} `:`<a href="${this.seturlOther(params,urlObj,'选择物品','fightsetItem',{keyId,generalId,thindId,page,subType,type:item.id})}">${item.name}</a> `}`
            if(type==item.id&&item.child){
                item.child.forEach((item1,index1) => {
                    subTypestr+=`${subType==item1.id?`${item1.name} `:`<a href="${this.seturlOther(params,urlObj,'选择物品','fightsetItem',{keyId,generalId,thindId,page,type:item.id,subType:item1.id})}">${item1.name}</a> `}`
                })
                subTypestr+='<br/>'
            }
        }) 
        typestr+='<br/>'
        //获取物品列表
        let [goodsList, totalItems] = await this.personGoodsEntity.findAndCount({
            where: { userId: Number(userId), good: { type, subType } },
            relations: ['good'],
            take: this.size, skip: (page - 1) * this.size
        })
        let contentStr='';
        goodsList.forEach((item,index) => {
            contentStr+=`<a href="${this.seturlOther(params,urlObj,'使用物品','fightsetKey',{generalId,thingObj:{name:item.good.name,id:item.id},keyId})}">${index + 1}.${item.good.name}x${item.count}</a></br>`
        })
        //分页
        let pageStr = this.setpage(params, urlObj, page, totalItems, {keyId,generalId,thindId,page,type,subType}, 'fightsetItem', '使用物品')

        let content = `${typestr}${contentStr}${subTypestr}${pageStr}
        ${backRouter}`
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    setpage(params, urlObj, page, totalItems, attrs, router, routerName?: string) {
        let pageStr = '';
        let totalPages = Math.ceil(totalItems / this.size);
        const createPageLink = (newPage, text) => {
            let newAttrs = { ...attrs, page: newPage };
            pageStr += `<a href="${this.seturlOther(params, urlObj, routerName, router, newAttrs)}">${text}</a> `;
        };
        if (page > 2) createPageLink(1, '首页');
        if (page !== 1) createPageLink(page - 1, '上一页');
        if (page < totalPages) createPageLink(page + 1, '下一页');
        if (page < totalPages - 1 && totalPages > 1) createPageLink(totalPages, '末页');
        return pageStr
    }
    //选择武将页面
    async chooseGeneral(sid: string, cmd: number, userId: string,{gpsId,targetID,isNpc,generalId=0}) {
        let msg=''
        if(generalId){
            let userInfo=await this.roleEntity.findOneBy({ id: Number(userId) })
            if(userInfo.food<8){
                msg=`你粮草不足</br>`
            }else{
                await this.dataSource.transaction(async (manager) => {
                    await manager.update(GeneralEntity,{id:Number(generalId)},{hpNow:()=>'hp'})
                    await manager.update(RoleEntity,{id:Number(userId)},{food:()=>'food -'+8})
                    await this.redisService.hset('user' + userId, 'userInfo',JSON.stringify(userInfo))
                })
                //写入日志
                let general=await this.generalService.getGeneralDetail(generalId)
                this.eventEmitter.emit('writeLogs', { userId, name: '供给粮草:' + general.name,food:8 })
                msg=`你供给粮草x8给${general.name}</br>`
            }
        }
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'chooseGeneral', title: '选择武将',service:'fight', params:{gpsId,targetID,isNpc} } }
        let generalList=await this.generalService.getGeneralList(userId)
        let generalsStr=''
        generalList.filter(item=>(item.hpNow>0&&!item.fightStatus)).forEach((item,index) => {
            generalsStr+=`<a href="${this.seturlOther(params,urlObj,'战场','fighting',{gpsId,targetID,isNpc,generalId:item.id})}">${index+1}.${item.name}部</a><br/>`
        });
        let dieGeneralStr=''
        generalList.filter(item=>item.hpNow==0).forEach((item,index) => {
            dieGeneralStr+=`${item.name}已经奄奄一息了，不能参战。请点击武将，供给粮草给此武将可恢复生命。<a href="${this.seturlOther(params,urlObj,'选择武将','chooseGeneral',{gpsId,targetID,isNpc,generalId:item.id})}">立即供给粮草</a><br/>`
        })
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `${msg}${dieGeneralStr}请选择其他空闲军队作战！<br/>
        你的空闲军队有：<br/>
        ${generalsStr}
        ${backRouter}`
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //战场列表 isEnd 1进行中  2已结束
    async fightList(sid: string, cmd: number, userId: string,{page=1,isEnd=1,changeStatus}) {
        let mystatus=1
        if(changeStatus){
            page=1
            isEnd=isEnd==1?2:1
        }
        if(isEnd==2){
            mystatus=Not(mystatus) as any
        }
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'fightList', title: '战场列表',service:'fight', params:{page,isEnd} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        //获取战场
        //查询fightGeneralEntity，根据userId分组 取出userId等于某个值的数据
        let [goodsList, totalItems] =await this.fightEntity.findAndCount({
            where:[{fightGenerals:{userId:Number(userId)},status:mystatus}],
            relations:['fightGenerals'],
            order:{createDate:'DESC'},
            take: this.size, skip: (page - 1) * this.size
        })
        let contentStr='';
        goodsList.forEach((item,index) => {
            contentStr+=`<a href="${this.seturlOther(params,urlObj,'战斗结束','fightEnd',{fightingId:item.id})}">${index + 1}.[${formatDate(item.createDate,2)}]${item.name}</a></br>`
        })
        //分页
        let pageStr = this.setpage(params, urlObj, page, totalItems, {isEnd}, 'fightList', '战场列表')
        let content = `${contentStr?'你的战场有：':'你目前没有战场'}</br>
        ${contentStr}
        ${pageStr?`${pageStr}<br/>`:''}
        ${`<a href="${this.seturlOther(params,urlObj,'战场列表','fightList',{page,changeStatus:1,isEnd})}">${isEnd==1?'已结束的战场':'进行中的战场'}</a></br>`}
        ${backRouter}`
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //模板
    async moban(sid: string, cmd: number, userId: string) {
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'home', title: '游戏首页',service:'fight', params:{} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `
        ${backRouter}`
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //设置返回路由
    async backRouter(userId, params, urlObj) {
        let str = ''
        //返回路由
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        if(routerList?.length>1){
            routerList.reverse().shift()
            routerList.forEach(element => {
                str += `<a href="${this.seturlOther(params, urlObj, element.title, element.name, element.params, element.service)}">返回${element.title}</a><br/>`
            });
        }
        return str
    }
    //获取上一级路由
    async getPrevRouter(userId) {
        let str = ''
        //返回路由 
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        return routerList[routerList.length-2]
    }
    /**
     * 
     * @param params cnd sid参数 
     * @param urlObj url集合
     * @param title 页面标题
     * @param name 页面名称
     * @param param 请求参数
     * @param service 页面服务名
     * @returns string 页面url
}
     */
    seturlOther(params, urlObj, title: string, name: string, attr = {}, service = 'fight') {
        params.cmd++
        urlObj[params.cmd] = { title, name, service, params: attr }
        return `/gcmd?sid=${params.sid}&cmd=${params.cmd}`
    }
    //获取技能模板 没有就缓存
    async getSkillTemplate(skillName:string) {
        let skillTemplate = JSON.parse(await this.redisService.hget('fight', 'skillTemplate'))
        if (!skillTemplate) {
            skillTemplate = await this.skillTemplateEntity.find()
            await this.redisService.hset('fight', 'skillTemplate', JSON.stringify(skillTemplate))      
        }
        return skillTemplate.find(item => item.name === skillName)
    }
    //处理任务怪
    async handelTaskNpc(userId,npcId) {
        let userTaskList=await this.gcmdService.getUserTaskList(userId)
        let taskInfo;let msg='';
        for(let i=0;i<taskListConfig.length;i++){
            let taskInfo=taskListConfig[i]
            let userTask=userTaskList.find(item=>item.taskId==taskInfo.id)
            if(userTask){
                let taskJson=this.otherService.getTaskListByTaskId(taskInfo.id,userTask.stepId)
                let taskInfo1=taskJson.steps[userTask.stepId-taskJson.initIndex]
                if(taskInfo1&&taskInfo1.condition.condition==2){
                    let npcInfo=await this.gcmdService.getNpcInfo(npcId)
                    let task=taskInfo1.condition.monsters?.find(item=>item.name==npcInfo.name)
                    if(task){
                        let info=await this.personNpcEntity.findOneBy({userId:userId,npcName:npcInfo.name})
                        if(!info){
                            info=new PersonNpcEntity()
                            info.userId=userId
                            info.npcName=npcInfo.name
                            info.count=1
                        }else{
                            info.count++
                        }
                        await this.personNpcEntity.save(info)
                        msg+=`任务：${npcInfo.name}:${info.count}/${task.num}</br>`
                    }
                }
            }
        }
        return msg
    }
    //创建建筑的定时任务
    async createFightTimer(item: FightEntity) {
        await this.tasksService.clearInterval('fight'+item.id)
        this.tasksService.createInterval(item.interval, 'fight' + item.id, () => {
            console.log('攻击',item.id)
            this.attack(item.id)
        })
    }
    //自动拾取
    async autoPick(goodsArr:FightGoodsEntity[]){
        let userInfo=JSON.parse(await this.redisService.hget('user'+this.userSetting.userId,'userInfo'))
        for(let i=0;i<goodsArr.length;i++){
            let good=goodsArr[i]
            good.status=2
            await this.goodsService.changePersonGoodByName(this.userSetting.userId,good.goodName,good.count,'add')
            await this.fightInfoEntity.insert({desc:`自动拾取${good.goodName}x${good.count}`,fightEvent:good.fight})
            this.eventEmitter.emit('writeLogs', { userId:userInfo.id, name: userInfo.name+'拾取物品:' + good.goodName+'x'+good.count+'战场id'+good.fight.id})
        }
        await this.fightGoodsEntity.save(goodsArr)
    }
}
