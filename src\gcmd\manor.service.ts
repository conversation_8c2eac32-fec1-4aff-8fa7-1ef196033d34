import { ManorEntity } from 'src/entities/manor.entity';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ModuleRef } from '@nestjs/core';
import { RoleEntity } from 'src/entities/role.entity';
import { DataSource, In, MoreThan, Not, Repository } from 'typeorm';
import { buildconfig, buildType, formatDuring, handlbuildfn, manorEventfn, manorPopulation } from 'src/utils/config';
import { ManorEventEntity } from 'src/entities/manorEvent.entity';
import { LogsEntity } from 'src/entities/logs.entity';
import { TasksService } from 'src/tasks/tasks.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { GeneralService } from './general.service';
import { TeamsEntity } from 'src/entities/teams.entity';
import { TeamRolesEntity } from 'src/entities/teamRoles.entity';
import { IsCaptain, IsTrue } from 'src/utils/types';
import { TeamJoinEntity } from 'src/entities/teamJoin.entity';
import { ChatEntity } from 'src/entities/chat.entity';
import { RedisService } from 'src/middleware/redis.service';
import { BlessEntity } from 'src/entities/general/bless.entity';
import { MountEntity } from 'src/entities/general/mount.entity';
import { mountConfig } from 'src/utils/config1';
import { CommonService } from 'src/middleware/common.service';
import { FightEntity } from 'src/entities/fight.entity';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { ManorLogEntity } from 'src/entities/good/manorLog.entity';
@Injectable()
//我的庄院
export class ManorService implements OnModuleInit {
    private readonly serviceName = 'manor'
    private readonly size = 20
    // private commonService: CommonService;

    constructor(
        private readonly moduleRef: ModuleRef,
        private readonly tasksService: TasksService,
        private eventEmitter: EventEmitter2,
        private readonly redisService: RedisService,
        private readonly commonService: CommonService,
        @InjectRepository(ManorEntity) private readonly manorEntity: Repository<ManorEntity>,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(ManorEventEntity) private readonly manorEventEntity: Repository<ManorEventEntity>,
        @InjectRepository(LogsEntity) private readonly logsEntity: Repository<LogsEntity>,
        @InjectRepository(TeamsEntity) private readonly teamsEntity: Repository<TeamsEntity>,
        @InjectRepository(BlessEntity) private readonly blessEntity: Repository<BlessEntity>,
        @InjectRepository(MountEntity) private readonly mountEntity: Repository<MountEntity>,
        @InjectRepository(TeamRolesEntity) private readonly teamRolesEntity: Repository<TeamRolesEntity>,
        @InjectRepository(TeamJoinEntity) private readonly teamJoinEntity: Repository<TeamJoinEntity>,
        @InjectRepository(ChatEntity) private readonly chatEntity: Repository<ChatEntity>,
        @InjectRepository(FightEntity) private readonly fightEntity: Repository<FightEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        @InjectRepository(ManorLogEntity) private readonly manorLogEntity: Repository<ManorLogEntity>,
        private dataSource: DataSource,
    ) {
        console.log('这里执行了吗1');
    }
    async onModuleInit() {
        console.log('ManorService onModuleInit 开始执行');

        // 延迟获取 CommonService 以避免循环依赖
        // this.commonService = this.moduleRef.get(CommonService, { strict: false });

        console.log('ManorService 成功获取 CommonService');

        let eventList: ManorEventEntity[] = await this.manorEventEntity.find({ where: { status: 1 } })
        eventList.forEach(item => {
            this.createManorTimer(item)
        })
        let blessList = await this.blessEntity.find({ where: { status: 1 } })
        blessList.forEach(item => {
            this.createBlessTimer(item)
        })
        let fightList = await this.fightEntity.find({ where: { status: 1 } })
        fightList.forEach(item => {
            setTimeout(() => {
                this.eventEmitter.emit('createFightTimer', item)
            }, 3000);
        })

        console.log('ManorService onModuleInit 执行完成');
    }
    //我的庄院
    async main(sid: string, cmd: number, userId: string) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'main', title: '我的庄院', service: this.serviceName, params: {} } }
        //获取返回路由
        let backRouter = await this.commonService.backRouter(userId, params, urlObj, 'main', this.serviceName)
        //获取建筑信息
        let manorInfoarr = await this.getManorInfo(userId, 1)
        let content = ''
        if (manorInfoarr.length) {
            let manorInfo = manorInfoarr[0]
            if (manorInfo.level == 0) {
                let manorEventInfo = await this.manorEventEntity.findOne({ where: { buildId: manorInfo.id }, select: ["completionTime"] })
                content = `[庄院管理]<br/>
                你正在建造决议大厅，剩余${formatDuring(new Date(manorEventInfo.completionTime).getTime() - new Date().getTime())}...<a href="${this.seturlOther(params, urlObj, '我的庄院', 'main')}">取消</a><br/>
                <a href="${this.seturlOther(params, urlObj, '我的庄院', 'main')}">刷新</a><br/>`
            } else {
                //所有建筑
                let manorInfo1 = await this.getManorInfo(userId) as ManorEntity[];
                let { eventNum, buildNUm } = manorEventfn(manorInfo.level);//获取庄园等级对应的可存在建筑和时间数量
                let manorEventInfo = await this.manorEventEntity.find({
                    where: { userId: Number(userId), status: 1 },
                })
                let str1 = ''
                let str2 = manorEventInfo.find(item => item.num1 == manorInfo.id)
                if (str2) {
                    str1 += `正在升级:剩余${formatDuring(new Date(str2.completionTime).getTime() - new Date().getTime())}...
                    <a href="${this.seturlOther(params, urlObj, '加速', 'useGoods', { hidden: true, paramsObj: { names: ['鲁班秘籍一', '鲁班秘籍二', '鲁班秘籍三', '鲁班秘籍四', '鲁班大全'], buildId: manorInfo.id, obj: {}, type: 'jiasu', hidden: true } })}">加速</a>`
                } else {
                    str1 += `<a href="${this.seturlOther(params, urlObj, '升级', 'build', { type: 'zhuangyuan', manorId: manorInfo.id, hidden: true })}">升级</a>`
                }
                //兵部
                let str3 = `兵部：`
                if (manorInfo1.filter(item => item.type == 10).length) {
                    let id = manorInfo1.filter(item => item.type == 10)[0].id
                    str3 += `<a href="${this.seturlOther(params, urlObj, '兵部', 'buildInfo', { manorId: id })}">查看</a>`
                } else {
                    str3 += `<a href="${this.seturlOther(params, urlObj, '建造', 'build', { type: 'bingbu' })}">建造</a>`
                }
                //马棚
                let str4 = `马棚：`
                if (manorInfo1.filter(item => item.type == 11).length) {
                    let id = manorInfo1.filter(item => item.type == 11)[0].id
                    str4 += `<a href="${this.seturlOther(params, urlObj, '马棚', 'buildInfo', { manorId: id })}">查看</a>`
                } else {
                    str4 += `<a href="${this.seturlOther(params, urlObj, '建造', 'build', { type: 'mapeng' })}">建造</a>`
                }
                content = `<p>[庄院管理]<br/> 
                决议大厅:<br/>
                等级:${manorInfo.level} [${str1}]<br/>
                建筑:${manorInfo1.filter(item => item.type < 12).length}/${buildNUm}<br/>
                事件:${manorEventInfo.length}/${eventNum}<br/>
                <a href="${this.seturlOther(params, urlObj, '事件列表', 'eventList')}">事件列表(${manorEventInfo.length})</a><br/>
                ------<br/>
                房屋:<a href="${this.seturlOther(params, urlObj, '房屋', 'buildList', { type: 'fangwu' })}">
                ${manorInfo1.filter(item => item.type == 2).length}间</a>&nbsp;
                <a href="${this.seturlOther(params, urlObj, '建造', 'build', { type: 'fangwu' })}">建造</a><br/>
                ${str3}<br/>
                ${str4}<br/>
                ------<br/>
                <a href="${this.seturlOther(params, urlObj, '庄院', 'main')}">刷新</a><br/>
                </p>`
            }
        } else {
            content = `[庄院管理]<br/>
            你尚未建造决议大厅，决议大厅是建造其他任何建筑的前提。<br/>
            <a href="${this.seturlOther(params, urlObj, '建造', 'build', { type: 'zhuangyuan' })}">立刻建造</a><br/>`
        }
        content += `${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //建筑列表
    async buildList(sid: string, cmd: number, userId: string, { type }) {
        // 1庄院 2房屋 7铁匠铺8裁缝铺9书院 10兵部 11马棚
        let name = buildType[type].name
        let manorType = buildType[type].type
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'buildList', title: name + '列表', service: this.serviceName, params: { type } } }
        let manorInfo = await this.getManorInfo(userId, manorType);
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let str = `你还没有建造任何${name}</br>`
        if (manorInfo.length) {
            let idArr = manorInfo.filter(item => item.status == 1).map(item => item.id)
            let manorEventInfo = await this.manorEventEntity.find({ where: { buildId: In(idArr),status:1 }, select: ['completionTime', 'buildId'] })
            str = `目前的${name}有:</br>`
            manorInfo.forEach((item, index) => {
                let str1 = ''
                if (item.status == 1) {
                    let manor = manorEventInfo.find(item1 => item1.buildId == item.id)
                    if (manor&&manor.completionTime) {
                        str1 += `[正在建造:剩余${formatDuring(manor.completionTime.getTime()-new Date().getTime())}]
                        <a href="${this.seturlOther(params, urlObj, '加速', 'useGoods', { hidden: true, paramsObj: { names: ['鲁班秘籍一', '鲁班秘籍二', '鲁班秘籍三', '鲁班秘籍四', '鲁班大全'], buildId: item.id, obj: {type}, type: 'jiasu', hidden: true } })}">加速</a> 
                        <a href="${this.seturlOther(params, urlObj, name, 'buildList', { type })}">刷新</a> 
                        <a href="${this.seturlOther(params, urlObj, '取消', 'cancelEvent', { manorId: item.id, page: 'buildList' })}">取消</a>`
                    }
                } else {
                    str1 += `[<a href="${this.seturlOther(params, urlObj, '升级', 'build', { type, manorId: item.id,hidden: true })}">升级</a>]`
                }
                str += `<a href="${this.seturlOther(params, urlObj, '建筑详情', 'buildInfo', { manorId: item.id })}">${index + 1}.${name}(${item.level}级)</a> ${str1}<br/>`
            })
        }
        let str1 = ''
        if (manorType == 2) {
            let userInfo = await this.roleEntity.findOne({ where: { id: Number(userId) } })
            str1 = `人口:${userInfo.currentCount}/${userInfo.count}</br>`
        }
        let content = `
        ${str}
        ${str1}
        <a href="${this.seturlOther(params, urlObj, '建造', 'build', { type })}">建造</a><br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //建筑详情
    async buildInfo(sid: string, cmd: number, userId: string, { manorId, eventType, eventId }) {
        const manorInfo = await this.manorEntity.findOne({ where: { id: Number(manorId) } })
        let name = '', desc = '', type = '';
        for (const key in buildType) {
            const element = buildType[key];
            if (element.type == manorInfo.type) {
                name = element.name
                desc = element.desc
                type = key
            }
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'buildInfo', title: name, service: this.serviceName, params: { manorId } } }
        let str1 = ''
        if (manorInfo.status == 2 && manorInfo.type != 10 && manorInfo.type != 11) {
            str1 = `<a href="${this.seturlOther(params, urlObj, '拆除', 'cancelBuild', { manorId, page: 'buildList' })}">拆除</a><br/>`
        }
        let str2 = ''
        if (manorInfo.type == 2) {
            str2 = `人口:${manorPopulation(manorInfo.level)}<br/>`
        } else if (manorInfo.type == 10) {
            let manorInfo1 = await this.manorEntity.find({ where: { userId: Number(userId) } })
            manorInfo1.forEach(element => {
                if (element.type == 12) {
                    str2 += `武将攻击术:${element.level}级 [<a href="${this.seturlOther(params, urlObj, '研究', 'build', { type: 'bingbu', manorId: element.id })}">研究</a>]<br/>`
                } else if (element.type == 13) {
                    str2 += `武将防御术:${element.level}级 [<a href="${this.seturlOther(params, urlObj, '研究', 'build', { type: 'bingbu', manorId: element.id })}">研究</a>]<br/>`
                } else if (element.type == 14) {
                    str2 += `士兵攻击术:${element.level}级 [<a href="${this.seturlOther(params, urlObj, '研究', 'build', { type: 'bingbu', manorId: element.id })}">研究</a>]<br/>`
                } else if (element.type == 15) {
                    str2 += `士兵防御术:${element.level}级 [<a href="${this.seturlOther(params, urlObj, '研究', 'build', { type: 'bingbu', manorId: element.id })}">研究</a>]`
                }
            })
        } else if (manorInfo.type == 11) {
            if (eventType == 1 && eventId) {
                //取消挂售
                let res = await this.mountEntity.update({ id: Number(eventId) }, { sellStatus: 1 })
                let name1 = res.affected == 1 ? `取消挂售成功` : '取消挂售失败'
                name = name1 + `<br/>` + name
            }
            if (eventType == 2 && eventId) {
                //赶走坐骑
                let res = await this.mountEntity.delete({ id: Number(eventId) })
                let name1 = res.affected == 1 ? `赶走坐骑成功` : '赶走坐骑失败'
                name = name1 + `<br/>` + name
            }
            let mountList = await this.mountEntity.find({ where: { userId: Number(userId) } })
            let unuse = mountList.filter(item => item.status == 1).length
            if (mountList.length) {
                str2 += `坐骑数量:${unuse}+${mountList.length - unuse}/${manorInfo.level * 4}<br/>`
                mountList.forEach((item, index) => {
                    let mountInfo = mountConfig[item.mountType]
                    let str = ''
                    if (item.sellStatus == 1) {
                        str = `
                        ${item.status === 1 ?
                                `<a href="${this.seturlOther(params, urlObj, '挂售', 'mountSell', { mountId: item.id })}">挂售</a>` : ''
                            }`
                    } else {
                        str = `<a href="${this.seturlOther(params, urlObj, '取消挂售', 'buildInfo', { manorId, eventId: item.id, eventType: 1 })}">取消挂售</a>`
                    }
                    str2 += `<a href="${this.seturlOther(params, urlObj, '坐骑详情', 'mountDetail', { manorId, mountId: item.id }, 'generalother')}">${index + 1}.${mountInfo.name}</a>
                    (${item.level}级,${item.status === 1 ? '闲置' : '外出'})
                    ${str}<br/>`
                })
            } else {
                str2 = `没有任何坐骑<br/>`
            }
        }
        let str3=''
        if(manorInfo.status==1){
            let manorEventInfo=await this.manorEventEntity.findOne({where:{buildId:manorId,status:1}})
            if(manorEventInfo){
                str3=`[升级中，还有${formatDuring(manorEventInfo.completionTime.getTime()-new Date().getTime())}完成]`
            }
        }else{
            str3=`<a href="${this.seturlOther(params, urlObj, '升级', 'build', { type, manorId,hidden: true })}">升级</a>`
        }
        //获取返回路由
        let backRouter = await this.commonService.backRouter(userId, params, urlObj,'buildInfo',this.serviceName)
        let content = `${name}<br/>
        ${desc}<br/>
        等级:${manorInfo.level}${str3}<br/>
        ${str2}
        ${str1}
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }

    //挂出销售
    async mountSell(sid: string, cmd: number, userId: number, { mountId }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'mountSell', title: '挂出销售', service: this.serviceName, params: { mountId } } }
        let mountInfo = await this.mountEntity.findOne({ where: { id: Number(mountId) } })
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = ''
        if (!mountInfo) {
            content = '没有这个坐骑<br/>'
        } else {
            let info = mountConfig[mountInfo.mountType]
            content = `请输入${info.name}的销售价格：</br>
            <form action="${this.seturlOther(params, urlObj, '挂出销售', 'mountSellPost', { thingId: mountId })}" method="post">
            销售价格:<input name="count8" type="number" min="1"/><br/>
            <input name="submit" type="submit" value="挂出销售"/></form>`
        }
        content += backRouter
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        await this.redisService.hset('user' + userId, 'token', String(userId), 1800)
        return content
    }
    //挂出销售提交
    async mountSellPost(userId: number, mountId: string, count: number, cmd: number, sid: string) {
        count = Math.abs(Number(count));
        let content = '';
        let res = await this.mountEntity.update({ id: Number(mountId) }, { price: count, sellStatus: 2 })
        if (res.affected > 0) {
            content = `挂出销售成功，价格为${count}<br/>`
        } else {
            content = `未知错误，请稍后再试<br/>`
        }
        let routerInfo = await this.commonService.getPrevRouter1(userId)
        content += await this[routerInfo.name](sid, cmd, userId, routerInfo.params)
        return content
    }


    //神行千里列表
    async shenxingqianli(sid: string, cmd: number, userId: string) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'shenxingqianli', title: '神行千里', service: 'gcmd', params: {} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let userInfo = await this.roleEntity.findOne({ where: { id: Number(userId) } })
        if (userInfo.gold < 200) {
            return `银两不足200两，无法使用神行千里<br/>${backRouter}`
        } else {
            await this.roleEntity.update({ id: Number(userId) }, { gold: () => `gold - 200` })
            //写入日志
            this.writeLogs({ userId, name: `神行千里`, gold: 200 })
        }
        let content = `你焚烧了200两银子，祭起神行千里(银两-200)……<br/>
            【世界地图】<br/>
            ----<br/>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 5050 }, 'gcmd')}">我的庄院</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 5851 }, 'gcmd')}">资源群山</a>
            <br/>----<br/>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 5853 }, 'gcmd')}">史家庄</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 5951 }, 'gcmd')}">少华山</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 6152 }, 'gcmd')}">桃花村</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 4853 }, 'gcmd')}">千年古墓</a>
            <br/>----<br/>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 6153 }, 'gcmd')}">桃花山</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 4352 }, 'gcmd')}">上京东</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 6986 }, 'gcmd')}">柴家庄</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 6988 }, 'gcmd')}">百兽山岭</a>
            <br/>----<br/>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 7131 }, 'gcmd')}">上沧州</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 3965 }, 'gcmd')}">黄泥冈</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 6986 }, 'gcmd')}">去郓城</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 9111 }, 'gcmd')}">狂盗山谷</a>
            <br/>----<br/>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 9110 }, 'gcmd')}">景阳岗</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 2267 }, 'gcmd')}">去沂岭</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 6986 }, 'gcmd')}">上梁山</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 3962 }, 'gcmd')}">万剑宝塔</a>
            <br/>----<br/>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 7062 }, 'gcmd')}">黑风岭</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 7089 }, 'gcmd')}">食人谷</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 4554 }, 'gcmd')}">太尉宝库</a>
            <a href="${this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: 7006 }, 'gcmd')}">冰魄洞</a>
            <br/>----<br/>
            ====<br/>
                ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        return content;
    }
    //事件列表
    async eventList(sid: string, cmd: number, userId: string, { page = 1 }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'eventList', title: '事件列表', service: 'general', params: {} } }
        //所有建筑
        let manorInfo = await this.getManorInfo(userId);
        let { eventNum } = manorEventfn(manorInfo.find(item => item.type == 1).level);//获取庄园等级对应的可存在建筑和时间数量
        let userInfo = await this.roleEntity.findOne({ where: { id: Number(userId) } })
        let generalList = await this.commonService.getGeneralList(userId)
        let soldierList = await this.commonService.getSoldierList(userId)
        let backRouter = await this.backRouter(userId, params, urlObj)
        let manorEventInfo = await this.manorEventEntity.find({ where: { userId: Number(userId), status: 1 } })
        let str = ''
        let arr = ["庄院", "房屋", "农田", "伐木场", "采石场", "铁矿场", "铁匠铺", "裁缝铺", "书院", "兵部", "马棚", "武将攻击术", "武将防御术", "士兵攻击术", "士兵防御术"];
        manorEventInfo.forEach((item, index) => {
            if (item.type == 1) {
                let manor = manorInfo.find(item1 => item1.id == item.buildId)
                if (manor) {
                    str += `${index + 1}.正在升级${arr[manor.type - 1]}[${manor.level}级](剩余${formatDuring(new Date(item.completionTime).getTime() - new Date().getTime())})<br/>`
                }
            }
            //招募
            if (item.type == 2) {
                let soldier = soldierList.find(item1 => item1.id == item.buildId)
                let general = generalList.find(item1 => item1.id == soldier.pipId)
                str += `${index + 1}.正在招募${general.name}部下的${soldier.name}[${item.num1}/${item.num2}](剩余${formatDuring(new Date(item.completionTime).getTime() - new Date().getTime())})<br/>`
            }
            //打造
            if (item.type == 3) {
                str += `${index + 1}.正在打造${item.roleType}:剩余[${item.num1}/${item.num2}]${formatDuring(new Date(item.completionTime).getTime() - new Date().getTime())})<br/>`
            }
            if (item.type == 4) {
                if (item.roleType == 'sol') {
                    let soldier = soldierList.find(item1 => item1.id == item.buildId)
                    str += `${index + 1}.正在升级士兵${soldier.name}[${soldier.level}级](剩余${formatDuring(new Date(item.completionTime).getTime() - new Date().getTime())})<br/>`
                } else if (item.roleType == 'gen') {
                    let general = generalList.find(item1 => item1.id == item.buildId)
                    str += `${index + 1}.正在升级武将${general.name}[${general.level}级](剩余${formatDuring(new Date(item.completionTime).getTime() - new Date().getTime())})<br/>`
                } else if (item.roleType == 'role') {
                    str += `${index + 1}.正在升级角色${userInfo.name}[${userInfo.level}级](剩余${formatDuring(new Date(item.completionTime).getTime() - new Date().getTime())})<br/>`
                }
            }
        })
        let content = `事件:${manorEventInfo.length}/${eventNum}<br/>
        目前进行中的事件：<br/>
        ${str}
        <a href="${this.seturlOther(params, urlObj, '事件列表', 'eventList', { page })}">刷新</a><br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //取消事件
    async cancelEvent(sid: string, cmd: number, userId: string, { manorId, page, handle }) {
        let msg = '';
        if (handle) {
            const manorInfo = await this.manorEntity
                .createQueryBuilder('manor')
                .leftJoinAndSelect(
                    ManorEventEntity,
                    'event',
                    'event.buildId = manor.id AND event.status = 1'
                )
                .where('manor.id = :manorId', { manorId: Number(manorId) })
                .getRawOne();
            let type = '';
            if (manorInfo && manorInfo.event_id) {
                for (const key in buildType) {
                    const element = buildType[key];
                    if (element.type == manorInfo.manor_type) {
                        type = key
                    }
                }
                let time1 = manorInfo.event_completionTime.getTime() - new Date().getTime()
                let time2 = manorInfo.event_completionTime.getTime() - manorInfo.event_create_date.getTime()
                let prop = time1 / time2
                let buildInfo = handlbuildfn(type, manorInfo.manor_level + 1)
                await this.dataSource.transaction(async (manager) => {
                    // Update resources
                    const updateResult = await manager.update(RoleEntity,
                        { id: Number(userId) },
                        {
                            food: () => `food + ${Math.floor(buildInfo.food * prop)}`,
                            wood: () => `wood + ${Math.floor(buildInfo.wood * prop)}`,
                            stone: () => `stone + ${Math.floor(buildInfo.stone * prop)}`,
                            iron: () => `iron + ${Math.floor(buildInfo.iron * prop)}`,
                            gold: () => `gold + ${Math.floor(buildInfo.gold * prop)}`,
                        }
                    );
                    if (updateResult.affected === 0) {
                        throw new Error('Failed to update user resources');
                    }
                    // Delete event
                    const eventResult = await manager.update(ManorEventEntity,
                        { id: manorInfo.event_id },
                        { status: 2 }
                    );
                    if (eventResult.affected === 0) {
                        throw new Error('Failed to delete manor event');
                    }
                    // Update manor status
                    if (manorInfo.manor_level == 0) {
                        await manager.delete(ManorEntity, { id: Number(manorId) })
                    } else {
                        await manager.update(ManorEntity,
                            { id: Number(manorId) },
                            { status: 2 }
                        );
                    }
                    msg = `取消成功,你收回了${Math.floor(buildInfo.food * prop)}粮草、${Math.floor(buildInfo.wood * prop)}木材、${Math.floor(buildInfo.stone * prop)}石料、${Math.floor(buildInfo.iron * prop)}生铁、${Math.floor(buildInfo.gold * prop)}银两<br/>`
                    this.writeLogs({
                        userId,
                        name: `取消${manorInfo.manor_type}事件`,
                        food: Math.floor(buildInfo.food * prop),
                        wood: Math.floor(buildInfo.wood * prop),
                        stone: Math.floor(buildInfo.stone * prop),
                        iron: Math.floor(buildInfo.iron * prop),
                        gold: Math.floor(buildInfo.gold * prop)
                    } as any)
                });
                if (msg) {
                    let route = await this.getPrevRouter(userId, page)
                    return msg + (await this[page](sid, cmd, userId, route.params))
                }
            }
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'cancelEvent', title: '取消事件', service: this.serviceName, params: { manorId, page } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `取消事件只能收回部分未用完的资源，确定要取消这个事件吗？<br/>
        <a href="${this.seturlOther(params, urlObj, '确定取消', 'cancelEvent', { manorId, handle: 1, page })}">确定取消</a><br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //拆掉建筑
    async cancelBuild(sid: string, cmd: number, userId: string, { manorId, handle, page }) {
        if (handle) {
            const manorInfo = await this.manorEntity.delete(Number(manorId))
            if (manorInfo.affected == 1) {
                await this.updateUserMaxPopulation(Number(userId))
                let route = await this.getPrevRouter(userId, page)
                return '拆除成功' + (await this[page](sid, cmd, userId, route.params))
            }
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'cancelBuild', title: '拆除', service: this.serviceName, params: { manorId, page } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `确定要拆除吗？<br/>
        <a href="${this.seturlOther(params, urlObj, '确定拆除', 'cancelBuild', { manorId, handle: 1, page })}">确定拆除</a><br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;

    }
    //我的庄院-建造/升级  handle函数表示执行操作  manorId存在表示是升级建筑，否则是建造
    async build(sid: string, cmd: number, userId: string, { type, manorId, handle,hidden=true }) {
        let errormsg = '', manorInfo: ManorEntity;
        if (manorId) {
            manorInfo = await this.manorEntity.findOne({ where: { id: Number(manorId) } })
        } else {
            manorInfo = new ManorEntity()
            manorInfo.level = 0
        }
        //拿到升级建筑所需信息
        let buildInfo = handlbuildfn(type, manorInfo.level + 1)
        //处理逻辑 返回上一级
        let userInfo = await this.roleEntity.findOne({ where: { id: Number(userId) } })
        if (handle) {
            if (
                userInfo.food >= buildInfo.food &&
                userInfo.wood >= buildInfo.wood &&
                userInfo.stone >= buildInfo.stone &&
                userInfo.iron >= buildInfo.iron &&
                userInfo.gold >= buildInfo.gold
            ) {
                let res = await this.canUpgradeManor(Number(userId), manorId)
                if (!res.status) {
                    errormsg = res.msg
                } else {
                    userInfo.food -= buildInfo.food
                    userInfo.wood -= buildInfo.wood
                    userInfo.stone -= buildInfo.stone
                    userInfo.iron -= buildInfo.iron
                    userInfo.gold -= buildInfo.gold
                    if (!manorId) {
                        manorInfo.userId = Number(userId)
                        manorInfo.type = buildconfig.type[type]
                    }
                    manorInfo.status = 1
                    let manorEventInfo = new ManorEventEntity()
                    manorEventInfo.userId = Number(userId)
                    manorEventInfo.type = 1
                    manorEventInfo.completionTime = new Date(Date.now() + buildInfo.time * 1000)
                    manorEventInfo.num1 = manorId
                    await this.dataSource.transaction(async (manager) => {
                        await manager.save(RoleEntity, userInfo)
                        manorInfo = await manager.save(ManorEntity, manorInfo)
                        manorEventInfo.buildId = manorInfo.id
                        await manager.save(ManorEventEntity, manorEventInfo)
                    });
                    if (!errormsg) {
                        //写入日志
                        this.writeLogs({ userId, name: `${manorId == 1 ? '建造' : '升级'}${buildInfo.name}`, potential: 0, food: buildInfo.food, wood: buildInfo.wood, stone: buildInfo.stone, iron: buildInfo.iron, gold: buildInfo.gold })
                        let cmdObj = await this.commonService.getNotHiddenRouter(userId)
                        this.createManorTimer(manorEventInfo)
                        let str = `你花费 ${buildInfo.food}粮草、${buildInfo.wood}木材、${buildInfo.stone}石料、${buildInfo.iron}生铁、${buildInfo.gold}银两${manorId ? '成功升级' : '成功建造'}了${buildInfo.name}`
                        return str + (await this[cmdObj.name](sid, cmd, userId, cmdObj.params))
                    }
                }
            } else {
                errormsg = `你没有足够的资源，无法建造${buildInfo.name}。<br/>`
            }
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'build', title: '建造', service: this.serviceName, params: { type, manorId, hidden: true } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
    
        let content = `${errormsg ? errormsg + '<br/>' : ''}${manorId ? '升级' : '建造'}${buildInfo.name}需要${buildInfo.gold}粮草、${buildInfo.wood}木材、${buildInfo.stone}石料、${buildInfo.iron}生铁、${buildInfo.gold}银两,时间x${formatDuring(buildInfo.time * 1000)}<br/>
        你有${userInfo.food}粮草、${userInfo.wood}木材、${userInfo.stone}石料、${userInfo.iron}生铁、${userInfo.gold}银两。<br/>${buildInfo.desc}<br/>
        <a href="${this.seturlOther(params, urlObj, '确认建造', 'build', { type, manorId, handle: 1, hidden: true })}">确定建造</a><br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //队伍页面
    /**
     * 
     * @param quitTeam 退出队伍
     * @param stopEnterTeam 禁止加入队伍 
     * @param teamId 队伍id
     * @param receivePotential 领取潜能
     * @param joinTeam 加入队伍
     * @param joinTeamConfirm 处理加入队伍的成员加入队伍  TRUE FALSE
     * @param kictOut 踢出队伍
     * @returns 
     */
    async team(sid: string, cmd: number, userId: string, { quitTeam, stopEnterTeam, createTeam, teamId, receivePotential, joinTeam, kictOut, joinTeamConfirm }) {
        let msg = '';

        //处理逻辑
        if (!teamId) {
            teamId = (await this.teamsEntity.findOne({ where: { teamRoles: { userId: Number(userId) } } }))?.id
        }
        let team = teamId ? (await this.teamsEntity.findOne({ where: { id: teamId }, relations: ['teamRoles', 'teamJoins'] })) : null
        let isSelfTeam = team?.teamRoles.find(item => item.isCaptain == 'YES').userId == Number(userId)
        if (team) {
            let captainId = team.teamRoles.find(item => item.isCaptain == 'YES').userId
            if (joinTeam && teamId) {
                msg = '操作失败'
                if (team.teamRoles.length < 5 && team.stopEnterTeam == 'NO') {
                    let userInfo = JSON.parse(await this.redisService.hget('user' + userId, 'userInfo'))
                    await this.teamJoinEntity.delete({ userId: Number(userId) })
                    let res = await this.teamJoinEntity.save({ team: team, userId: Number(userId), userName: userInfo.name, status: 1 })
                    if (res) {
                        this.eventEmitter.emit('sendMsgToRole', { type: 3, content: `${userInfo.name}请求加入队伍,请前往队伍页面查看`, userId: captainId })
                        msg = '你成功发出了加入队伍的请求，请等候队长批准!'
                    }
                }

            }
            if (stopEnterTeam) {
                await this.teamsEntity.update({ id: teamId }, { stopEnterTeam: stopEnterTeam == 'YES' ? IsTrue.NO : IsTrue.YES })
                team.stopEnterTeam = stopEnterTeam == 'YES' ? IsTrue.NO : IsTrue.YES
            }
            if (quitTeam) {
                let arr = []
                let res = team.teamRoles.filter(item => item.isCaptain == 'YES')
                if (res.length && res[0].userId == Number(userId)) {
                    team.teamRoles.map(item => arr.push(item.id))
                    await this.dataSource.transaction(async (manager) => {
                        await manager.delete(TeamRolesEntity, { id: In(arr) })
                        await manager.delete(TeamsEntity, { id: team.id })
                    })
                } else {
                    let res = team.teamRoles.filter(item => item.userId == Number(userId))
                    if (res.length) {
                        await this.teamRolesEntity.delete(res[0].id)
                    }
                }
                team = null
                this.eventEmitter.emit('clearDungeon', userId)
            }
            if (receivePotential) {
                //领取潜能
                let teamRole = team.teamRoles.find(item => item.userId == Number(userId))
                if (teamRole && teamRole.potential > 0) {
                    let potential = Math.floor(teamRole.potential * 0.05)
                    await this.teamRolesEntity.update({ id: teamRole.id }, { potential: 0 })
                    await this.roleEntity.update({ id: Number(userId) }, { potential: () => 'potential + ' + potential })
                    this.writeLogs({
                        userId,
                        name: '队伍领取潜能',
                        potential
                    } as any)
                }
            }
            //踢出
            if (kictOut) {
                let teamRoleInfo = team.teamRoles.find(item => item.id == Number(kictOut))
                let res = await this.teamRolesEntity.delete({ id: kictOut })
                if (res.affected == 1) {
                    msg = '踢出成功'
                    this.eventEmitter.emit('sendMsgToRole', { type: 3, content: `你被踢出队伍`, userId: teamRoleInfo.userId })
                    team = await this.teamsEntity.findOne({ where: { id: teamId }, relations: ['teamRoles', 'teamJoins'] })
                }
            }
            //允许加入
            if (joinTeamConfirm) {
                let arr = joinTeamConfirm.split('&')
                if (team.teamRoles.length > 5) {
                    msg = '队伍人数已满，无法加入！'
                } else if (team.stopEnterTeam == 'YES') {
                    msg = '队伍禁止加入！'
                } else {
                    await this.dataSource.transaction(async (manager) => {
                        let res = await manager.update(TeamJoinEntity, { id: arr[1], status: 1 }, { status: arr[0] == 'YES' ? 2 : 3 })
                        if (res.affected) {
                            let res = await this.teamJoinEntity.findOne({ where: { id: arr[1] } })
                            if (arr[0] == 'YES') {
                                let userInfo = JSON.parse(await this.redisService.hget('user' + res.userId, 'userInfo'))
                                await manager.insert(TeamRolesEntity, { team: team, userId: userInfo.id, userName: userInfo.name })
                                msg = `你已同意${userInfo.name}加入队伍！`
                                this.eventEmitter.emit('sendMsgToRole', { type: 3, content: `${team.name}已同意你加入队伍`, userId: res.userId })
                            } else {
                                msg = '你已拒绝其加入队伍'
                                this.eventEmitter.emit('sendMsgToRole', { type: 3, content: `${team.name}拒绝你加入队伍`, userId: res.userId })
                            }
                        } else {
                            msg = '操作失败,申请不存在'
                        }

                    })
                    team = await this.teamsEntity.findOne({ where: { id: teamId }, relations: ['teamRoles', 'teamJoins'] })
                }
            }
        } else {
            if (createTeam) {
                let userInfo = JSON.parse(await this.redisService.hget('user' + userId, 'userInfo'))
                let teamRole = new TeamRolesEntity()
                teamRole.isCaptain = IsCaptain.YES
                teamRole.userName = userInfo.name
                teamRole.userId = Number(userId)
                await this.teamRolesEntity.save(teamRole)
                let team1 = new TeamsEntity()
                team1.name = userInfo.name
                team1.teamRoles = [teamRole]
                await this.teamsEntity.save(team1)
                team = await this.teamsEntity.findOne({ where: { teamRoles: { userId: Number(userId) } }, relations: ['teamRoles', 'teamJoins'] })
            }
        }
        // 渲染页面
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'team', title: '队伍', service: this.serviceName, params: { teamId } } }
        let str = '';
        if (team) {
            let str1 = '尚无其他成员<br/>'
            team.teamRoles.filter(item => item.isCaptain != 'YES').forEach((item, index) => {
                if (index == 0) str1 = ''
                let str = isSelfTeam ? `<a href="${this.seturlOther(params, urlObj, '队伍', 'team', { kictOut: item.id, teamId })}">踢出</a>` : ''
                str1 += `<a href="${this.seturlOther(params, urlObj, item.userName, 'myStatus', { targetUserId: item.userId }, 'gcmd')}">${item.userName}</a> ${str}<br/>`
            })
            let str2 = ''
            let teamRole = team.teamRoles.find(item => item.userId == Number(userId))
            if (teamRole) {
                str2 = `潜能分享:${teamRole.potential} x 5% = ${Math.floor(teamRole.potential * 0.05)} ${teamRole.potential > 0 ? '<a href="' + this.seturlOther(params, urlObj, '队伍', 'team', { receivePotential: 'YES' }) + '">领取</a>' : ''}<br/>`
            }
            let str3 = ''
            if (isSelfTeam) {
                str3 = `<a href="${this.seturlOther(params, urlObj, '队伍', 'team', { stopEnterTeam: team.stopEnterTeam })}">${team.stopEnterTeam == 'YES' ? '允许加入队伍' : '禁止加入队伍'}</a><br/>`
            } else {
                if (!teamRole && team.teamRoles.length < 5 && team.stopEnterTeam == 'NO') {
                    str3 = `<a href="${this.seturlOther(params, urlObj, '队伍', 'team', { joinTeam: 'YES', teamId })}">加入队伍</a><br/>`
                }
            }
            let str4 = ''//申请列表
            if (isSelfTeam) {
                team.teamJoins.filter(item => item.status == 1).forEach((item, index) => {
                    if (index == 0) str4 = '请求加入：</br>'
                    str4 += `<a href="${this.seturlOther(params, urlObj, '队伍', 'team', {})}">${item.userName}</a> 
                    <a href="${this.seturlOther(params, urlObj, '队伍', 'team', { joinTeamConfirm: 'YES&' + item.id, teamId })}">允许</a>
                    <a href="${this.seturlOther(params, urlObj, '队伍', 'team', { joinTeamConfirm: 'NO&' + item.id, teamId })}">拒绝</a><br/>`
                })
            }
            let str5 = teamRole ? `<a href="${this.seturlOther(params, urlObj, '队伍', 'confirmExitTeam', { teamId })}">退出队伍</a></br>` : ''
            let userInfo = team.teamRoles.filter(item => item.isCaptain == 'YES')[0]
            str = `[${team.name}]<br/>
            人数:1/6<br/>
            队长:<a href="${this.seturlOther(params, urlObj, userInfo.userName, 'myStatus', { targetUserId: Number(userId) == userInfo.userId ? null : userInfo.userId }, 'gcmd')}">${userInfo.userName}</a><br/>
            ${str2}
            成员:</br>
            ${str1}
            ${str4}
            ${str3}
            ${str5}
            `
        } else {
            str += `你目前尚未加入任何队伍，你要创建队伍吗?</br>
            <a href="${this.seturlOther(params, urlObj, '队伍', 'team', { createTeam: 'YES' })}">创建队伍</a></br>`
        }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `${msg ? msg + '<br/>' : ''}${str} 
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //确定退出队伍
    async confirmExitTeam(sid: string, cmd: number, userId: string) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'home', title: '退出队伍', service: this.serviceName, params: {} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `你确定要退出当前队伍吗？</br>
        <a href="${this.seturlOther(params, urlObj, '队伍', 'team', { quitTeam: 'YES' })}">确定退出</a></br>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //功能菜单
    async settingMenu(sid: string, cmd: number, userId: string) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'settingMenu', title: '功能菜单', service: this.serviceName, params: {} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `[功能设置]<br/>
                客服微信:xxx(提交游戏问题,添加时备注分区,角色名字)<br/>
                <br/>
                提示:为确保帐号安全,系统禁止玩家私下帐号交易.一旦发现,将永久封号处理(不管该角色在买家还是卖家手中)!确有帐号私下交易需要,双方可联系客服处理!<br/>
                <br/>
                <a href="${this.seturlOther(params, urlObj, '神行千里', 'shenxingqianli')}">神行千里</a>-<a href="">我的住房</a><br/>
                <br/>
                <a href="">帮派列表</a>-
                <a href="">国家系统</a>-
                <a href="${this.seturlOther(params, urlObj, '高手排行', 'rankList', { type: 1 }, 'generalother')}">高手排行</a><br/>
                <a href="${this.seturlOther(params, urlObj, '查找玩家', 'searchPlayer', {}, 'generalother')}">查找玩家</a><br/>
                <br/>
                <a href="${this.seturlOther(params, urlObj, '战场设置', 'battleSetting', {}, 'generalother')}">战场设置</a>-
                <a href="${this.seturlOther(params, urlObj, '个性设置', 'personalSetting', {}, 'generalother')}">个性设置</a>-
                <a href="">快捷设置</a><br/>
                <br/>
                <a href="">攻略介绍</a><br/>
                ----<br/>
                <a href="${this.seturlOther(params, urlObj, '退出登录', 'logout')}">退出登录</a><br/>
                ----<br/>
                ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //退出登录
    async logout(sid: string, cmd: number, userId: string) {
        //清除缓存
        await this.redisService.del('user' + userId)
        await this.redisService.del('chatuser' + userId)
        return `你已成功退出登录</br>
        <a href="/users/login">重新登陆</a>`;
    }
    //模板
    async moban(sid: string, cmd: number, userId: string) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'home', title: '游戏首页', service: this.serviceName, params: {} } }
        let content = ``
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //获取建筑详情 所有建筑  type 建筑类型 默认获取全部
    async getManorInfo(userId: string, type?: number) {
        let manorInfo = await this.manorEntity.find({ where: { userId: Number(userId) } });
        if (type) {
            manorInfo = manorInfo.filter(item => item.type == type)
        }
        return manorInfo
    }
    //设置返回路由
    async backRouter(userId, params, urlObj) {
        //删除重复的路由
        function removeDuplicates(arr) {
            if (!arr || arr.length <= 1) return arr;
            const firstName = arr[0];
            let lastIndex = -1;
            for (let i = arr.length - 1; i >= 0; i--) {
                if (arr[i].service === firstName.service && arr[i].name === firstName.name) {
                    lastIndex = i;
                    break;
                }
            }
            if (lastIndex > 0) {
                return arr.slice(lastIndex);
            }
            return arr;
        }
        let str = ''
        //返回路由
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        routerList.reverse().shift()
        routerList = removeDuplicates(routerList)
        routerList.forEach(element => {
            if (!element.params.hidden) {
                str += `<a href="${this.seturlOther(params, urlObj, element.title, element.name, element.params, element.service)}">返回${element.title}</a><br/>`
            }
        });
        return str
    }
    //
    //获取上一级路由 传递page就是获取指定页面的参数
    async getPrevRouter(userId, page?: string) {
        let str = ''
        //返回路由 
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        if (page) {
            return routerList.find(item => item.name == page)
        }
        routerList.reverse().shift()
        routerList.reverse().shift()
        let result = routerList.shift()
        return result

    }
    /**
     * 
     * @param params cnd sid参数 
     * @param urlObj url集合
     * @param title 页面标题
     * @param name 页面名称
     * @param param 请求参数
     * @param service 页面服务名
     * @returns string 页面url
}
     */
    seturlOther(params, urlObj, title: string, name: string, attr = {}, service = 'manor') {
        params.cmd++
        urlObj[params.cmd] = { title, name, service, params: attr }
        return `/gcmd?sid=${params.sid}&cmd=${params.cmd}`
    }
    //判断建筑能不能升级
    async canUpgradeManor(userId: number, manorId: number) {
        let res = { status: false, msg: '' }
        let manorInfo = await this.manorEntity.find({ where: { userId } })
        let manorNow = manorInfo.find(item => item.id == manorId)
        //事件是否满了
        let manorMain = manorInfo.find(item => item.type == 1)
        if (!manorMain) {
            res.status = true
            return res
        }
        let { eventNum, buildNUm } = manorEventfn(manorMain.level);//获取庄园等级对应的可存在建筑和时间数量
        let eventCount = await this.manorEventEntity.count({ where: { userId, status: 1 } })
        if (eventCount >= eventNum) {
            res.msg = '事件已满,请先完成事件或升级庄院'
            return res
        }
        //建筑是否满了
        if (!manorId && manorInfo.filter(item => item.type < 12).length >= buildNUm) {
            res.msg = '建筑已满,请升级庄院'
            return res
        }
        if (!manorNow) {
            res.status = true
            return res
        }
        if (manorNow.status == 1) {
            res.msg = '建筑正在升级'
            return res
        }
        if (manorNow.type == 1) {
            //庄院
            let userInfo = await this.roleEntity.findOne({ where: { id: userId } })
            if ((manorNow.level-1) * 10 > userInfo.level) {
                res.msg = '需要角色等级达到' + (manorNow.level-1) * 10
                return res
            }
        }
        if ([2, 3, 4, 5, 6, 7, 8, 9, 10, 11].includes(manorNow.type)) {
            let mainManor = manorInfo.find(item => item.type == 1)
            if (manorNow.level == mainManor.level) {
                res.msg = '庄院等级不足'
                return res
            }
        }
        if ([12, 13, 14, 15].includes(manorNow.type)) {
            let mainManor = manorInfo.find(item => item.type == 10)
            if (manorNow.level == mainManor.level) {
                res.msg = '兵部等级不足'
                return res
            }
        }

        res.status = true
        return res
    }
    //写入日志
    @OnEvent('writeLogs')
    async writeLogs({ userId, name, food, potential, wood, stone, iron, gold }: any) {
        console.log('写入日志', name, food, wood, stone, iron, gold, potential);
        let logsInfo = new LogsEntity()
        logsInfo.userId = Number(userId)
        logsInfo.name = name || 0
        logsInfo.potential = potential || 0
        logsInfo.food = food || 0
        logsInfo.wood = wood || 0
        logsInfo.stone = stone || 0
        logsInfo.iron = iron || 0
        logsInfo.gold = gold || 0
        await this.logsEntity.save(logsInfo)
    }
    //创建建筑的定时任务
    async createManorTimer(item: ManorEventEntity) {
        let time: Date | number = new Date(item.completionTime);
        let taskName = 'createTimer'
        //如果时间小于当前时间，则时间+5秒
        let taskNameArr = ['manorEvent', 'recruit', 'build', 'upgrade'];//建筑事件，招募，打造，升级
        if (time <= new Date()) time = new Date(new Date().getTime() + 5000)
        //根据任务类型判断是创建定时任务还是创建间隔任务
        if (item.taskType == 2 || item.taskType == 3) {
            taskName = 'createInterval'
            time = item.loopTime * 1000
        }
        if (item.status == 2) {
            if (taskName == 'createTimer') {
                this.tasksService.clearTimer(taskNameArr[item.type - 1] + item.id)
            } else {
                this.tasksService.clearInterval(taskNameArr[item.type - 1] + item.id)
            }
            return
        }
        this.tasksService[taskName](time, taskNameArr[item.type - 1] + item.id, () => {
            if (item.type == 1) {
                this.createManorTimer1(item)
            } else if (item.type == 2) {
                //招募任务
                this.eventEmitter.emit('createRecruitTaskTimer', item.id)
            } else if (item.type == 3) {
                //打造任务
            } else {
                //升级任务
                this.eventEmitter.emit('createUpgradeTaskTimer', item.id)
            }


        })
    }
    //创建祝福状态定时器
    @OnEvent('createBlessTimer')
    async createBlessTimer(item: BlessEntity) {
        let time: Date | number = new Date(item.endTime);
        if (item.status == 2) {
            this.tasksService.clearTimer('bless' + item.id)
            return
        }
        //如果时间小于当前时间，则时间+5秒
        if (time <= new Date()) time = new Date(new Date().getTime() + 5000)
        // this.tasksService.getTaskList
        this.tasksService.createTimer(time, 'bless' + item.id, () => {
            this.blessStatusExpire(item)
        })
    }
    //建筑升级
    async createManorTimer1(item: ManorEventEntity) {
        await this.dataSource.transaction(async (manager) => {
            await manager.update(ManorEntity, { id: item.buildId, status: 1 }, { status: 2, level: () => "level + " + 1 })
            await manager.update(ManorEventEntity, { id: item.id, status: 1 }, { status: 2 })
            await this.redisService.hdel('user' + item.userId, 'manorInfo')
        })
        let manorInfo = await this.manorEntity.findOne({ where: { id: item.buildId } })
        let arr = ["庄院", "房屋", "农田", "伐木场", "采石场", "铁矿场", "铁匠铺", "裁缝铺", "书院", "兵部", "马棚", "武将攻击术", "武将防御术", "士兵攻击术", "士兵防御术"];
        if (manorInfo.type == 2) {
            //更新人口上限
            await this.updateUserMaxPopulation(item.userId)
        }
        if (manorInfo.type == 10 && manorInfo.level == 1) {
            let arr = [
                { type: 12, userId: item.userId, status: 2 },
                { type: 13, userId: item.userId, status: 2 },
                { type: 14, userId: item.userId, status: 2 },
                { type: 15, userId: item.userId, status: 2 }
            ]
            await this.manorEntity.save(arr)
        }
        //添加系统通知
        let chatEntity = new ChatEntity()
        chatEntity.toUserId = item.userId
        chatEntity.type = 3
        chatEntity.content = `${arr[manorInfo.type - 1]}升级完成`
        await this.chatEntity.save(chatEntity)
        this.eventEmitter.emit('sendMsgToRole', chatEntity)
    }
    //更新人口上限
    async updateUserMaxPopulation(userId: number) {
        let manorList = await this.manorEntity.find({ where: { userId, type: 2 } })
        let maxPopulation = 5
        manorList.forEach(item => {
            maxPopulation += manorPopulation(item.level)
        })
        //人物称号叠加 待处理
        await this.roleEntity.update({ id: userId }, { count: maxPopulation })
    }
    //祝福状态过期
    async blessStatusExpire(item: BlessEntity) {
        let blessId = item.id
        let blessInfo = await this.blessEntity.findOne({ where: { id: blessId } })
        if (blessInfo) {
            let endTime = (new Date(item.endTime)).getTime();
            if (endTime <= (new Date()).getTime()) {
                await this.blessEntity.update({ id: blessId }, { status: 2 })
            }
        }
    }
    //使用物品 用于显示某一类物品的情况 paramsObj={name:['鲁班秘籍一','鲁班秘籍二']}
    async useGoods(sid: string, cmd: number, userId: string, { page = 1, paramsObj, thingId = null }) {
        let msg = ''
        if (thingId) {
            let goodInfo = await this.personGoodsEntity.findOne({ where: { id: thingId }, relations: ['good'] })
            if (goodInfo) {
                if (paramsObj.type == 'jiasu') {//加速
                    let manorInfo = await this.manorEntity.findOne({ where: { id: paramsObj.buildId } })
                    if(!manorInfo){
                        console.log('建筑不存在',paramsObj.buildId);
                        return
                    }
                    let manorLog = await this.manorLogEntity.find({ where: { buildId: paramsObj.buildId,buildLevel:manorInfo.level } })
                    if (manorLog && manorLog.length) {
                        if (goodInfo.good.name == '鲁班大全') {
                            if (manorLog.filter(item => item.goodName == '鲁班大全').length >= 3) {
                                msg = '加速次数已达上限,一次升级过程最多只能使用各类鲁班秘籍3本和鲁班大全3本<br/>'
                            }
                        } else {
                            if (manorLog.filter(item => item.goodName != '鲁班大全').length >= 3) {
                                msg = '加速次数已达上限,一次升级过程最多只能使用各类鲁班秘籍3本和鲁班大全3本<br/>'
                            }
                        }
                    }
                    if (!msg) {
                        let mountInfo:any = await this.manorEventEntity.findOne({ where: { buildId: paramsObj.buildId, status: 1 } })
                        if (goodInfo.good.name == '鲁班大全') {
                            //减少剩余时间completionTime 的百分之30
                            let completionTime = (new Date(mountInfo.completionTime)).getTime()
                            let completionTimeNew = Math.floor(completionTime - (completionTime - (new Date().getTime())) * 0.3)
                            await this.manorEventEntity.update({ id: mountInfo.id }, { completionTime: new Date(completionTimeNew) })
                        }
                        let objtemp = { 鲁班秘籍一: 2, 鲁班秘籍二: 5, 鲁班秘籍三: 15, 鲁班秘籍四: 30 }
                        if (Object.keys(objtemp).includes(goodInfo.good.name)) {
                            //减少剩余时间completionTime 的2小时
                            let completionTime = (new Date(mountInfo.completionTime)).getTime()
                            let completionTimeNew = completionTime - objtemp[goodInfo.good.name] * 60 * 60 * 1000
                            //如果小于现在时间 就设置成完成
                            let where: any = { completionTime: completionTimeNew }
                            if (completionTimeNew < new Date().getTime()) {
                                await this.createManorTimer1(mountInfo)
                            } else {
                                where.completionTime = new Date(where.completionTime)
                                await this.manorEventEntity.update({ id: mountInfo.id }, where)
                            }

                        }
                        //添加使用记录
                        await this.manorLogEntity.save({buildId:paramsObj.buildId,goodName:goodInfo.good.name,buildLevel:manorInfo.level})

                        await this.personGoodsEntity.update({ id: thingId }, { count: () => "count-1" })
                        msg = '加速成功<br/>'
                        let routerInfo = await this.commonService.getNotHiddenRouter(userId)
                        return msg + await this[routerInfo.name](sid, cmd, userId, paramsObj.obj)
                    }
                }
            } else {
                msg = '物品不存在<br/>'
            }

        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'useGoods', title: '物品列表', service: 'general', params: { paramsObj, page, hidden: true } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        //获取物品列表
        let [goodsList] = await this.personGoodsEntity.findAndCount({
            where: { userId: Number(userId), good: { name: In(paramsObj.names) }, count: MoreThan(0) },
            relations: ['good'],
        })
        let contentStr = '';
        goodsList.forEach((item, index) => {
            contentStr += `<a href="${this.seturlOther(params, urlObj, '使用物品', 'useGoods', { paramsObj, page, thingId: item.id, hidden: true })}">${index + 1}.${item.good.name}x${item.count}</a></br>`
        })
        !contentStr && (msg = '没有可使用的物品<br/>')
        let content = `${msg}请选择物品<br/>${contentStr}
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }

}
