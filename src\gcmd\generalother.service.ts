import { OtherSeaService } from './config/otherSea.service';
import { FightEntity } from './../entities/fight.entity';
import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GeneralEntity } from 'src/entities/general.entity';
import { RoleEntity } from 'src/entities/role.entity';
import { DataSource, Equal, In, Like, MoreThan, Repository, UpdateResult, MoreThanOrEqual, Not } from 'typeorm';
import { checkMaxLevel, equipmentMountType, equipmentType, formatDuring, generalTemplate, getGoodType, getHpByLevel, goodsType, hunpofn, isPureNumber, jingmaiFn, roleUpgrade, soldierTemplate, weaponType } from 'src/utils/config'
import { SoldierEntity } from 'src/entities/soldier.entity';
import { SkillEntity } from 'src/entities/skill.entity';
import { ManorEventEntity } from 'src/entities/manorEvent.entity';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CommonService } from 'src/middleware/common.service';
import { TasksService } from 'src/tasks/tasks.service';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { EquipEntity } from 'src/entities/equip.entity';
import { GcmdService } from './gcmd.service';
import { GoodsService } from './goods.service';
import { EquipmentEntity } from 'src/entities/equipment/equipment.entity';
import { GeneralEquip } from 'src/entities/general/generalequip.entity';
import { RedisService } from 'src/middleware/redis.service';
import { SoldierNum } from 'src/entities/general/soldiernum.entity';
import { GeneralService } from './general.service';
import { BlessEntity } from 'src/entities/general/bless.entity';
import { MountEntity } from 'src/entities/general/mount.entity';
import { dungeonConfig, mountConfig, mountEquip } from 'src/utils/config1';
import { PersonGoodsHoleEntity } from 'src/entities/personGoodHole.entity';
import { GoodEntity } from 'src/entities/goods.entity';
import { ConfigEntity } from 'src/entities/config.entity';
import { DungeonLogEntity } from 'src/entities/dungeonLog.entity';
import { NpcEntity } from 'src/entities/npc.entity';
import { TeamRolesEntity } from 'src/entities/teamRoles.entity';
import { TeamsEntity } from 'src/entities/teams.entity';
import { IsCaptain } from 'src/utils/types';
import { UserConfigProvider } from 'src/common/user-config.provider';
@Injectable()
//我的武将
export class GeneralotherService {
    private readonly serviceName = 'generalother'
    private readonly size = 20
    constructor(
        private readonly commonService: CommonService,
        private readonly tasksService: TasksService,
        private readonly goodsService: GoodsService,
        private readonly otherSeaService: OtherSeaService,
        private readonly userConfigProvider: UserConfigProvider,
        @Inject(forwardRef(() => GeneralService)) private readonly generalService: GeneralService,
        @InjectRepository(TeamsEntity) private readonly teamsEntity: Repository<TeamsEntity>,
        @InjectRepository(MountEntity) private readonly mountEntity: Repository<MountEntity>,
        @Inject(forwardRef(() => GcmdService)) private readonly gcmd: GcmdService,
        private eventEmitter: EventEmitter2,
        private readonly redis: RedisService,
        @InjectRepository(GeneralEntity) private readonly generalEntity: Repository<GeneralEntity>,
        @InjectRepository(TeamRolesEntity) private readonly teamRolesEntity: Repository<TeamRolesEntity>,
        @InjectRepository(NpcEntity) private readonly npcEntity: Repository<NpcEntity>,
        @InjectRepository(DungeonLogEntity) private readonly dungeonLogEntity: Repository<DungeonLogEntity>,
        @InjectRepository(ConfigEntity) private readonly configEntity: Repository<ConfigEntity>,
        @InjectRepository(SoldierNum) private readonly soldierNumEntity: Repository<SoldierNum>,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(SoldierEntity) private readonly soldierEntity: Repository<SoldierEntity>,
        @InjectRepository(BlessEntity) private readonly blessEntity: Repository<BlessEntity>,
        @InjectRepository(SkillEntity) private readonly skillEntity: Repository<SkillEntity>,
        @InjectRepository(EquipmentEntity) private readonly equipmentEntity: Repository<EquipmentEntity>,//装备
        @InjectRepository(GeneralEquip) private readonly generalEquipEntity: Repository<GeneralEquip>,
        @InjectRepository(ManorEventEntity) private readonly manorEventEntity: Repository<ManorEventEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        @InjectRepository(EquipEntity) private readonly equipEntity: Repository<EquipEntity>,
        @InjectRepository(FightEntity) private readonly fightEntity: Repository<FightEntity>,
        private dataSource: DataSource,
    ) {

     }
    //散部士兵列表
    async sanbuSoldierList(sid: string, cmd: number, userId: number, { generalId }) {
        let msg = '';
        
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'generalList', title: '武将列表', service: this.serviceName, params: { generalId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let soldierList = await this.soldierNumEntity.find({ where: {userId} })
        let str = '散部当前没有士兵<br/>'
        soldierList.forEach((item, index) => {
            index===0&&(str='')
            let str1=''
            if(item.isSold==1){
                str1=`(挂售中,价格为:${item.price})`
            }

            str+=`<a href="${this.seturlOther(params, urlObj, '士兵详情', 'sanbuSoldierDetail',{sanbuId:item.id})}">${index+1}.${item.soldierName}(${item.soldierNum}名)${str1}</a><br/>`
        });
        //设置返回
        let content = `你的散部士兵有：<br/>
        ${str}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //散部士兵详情
    async sanbuSoldierDetail(sid: string, cmd: number, userId: number, { sanbuId,type }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'generalList', title: '士兵详情', service: this.serviceName, params: { sanbuId } } }
        let msg=''
        if(type=='chexiao'){
            let res = await this.soldierNumEntity.update({ id: sanbuId }, { isSold: 0, price: 0 });
            msg=res.affected>0?`撤销挂售成功`:`撤销挂售失败`
        }

        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let soldierTem=await this.soldierNumEntity.findOne({ where: { id: sanbuId } })
        let soldierInfo=soldierTemplate[soldierTem.soldierType]
        let str=''
        if(soldierTem.isSold==1){
            str=`<a href="${this.seturlOther(params, urlObj, '士兵详情', 'sanbuSoldierDetail',{sanbuId,type:'chexiao'})}">撤销挂售</a>`
        }else{
            str=`<a href="${this.seturlOther(params, urlObj, '挂出销售', 'sanbuSoldierSell',{soldierName:soldierInfo.name})}">挂出销售</a>`
        }
        //设置返回
        let content = `${msg}${soldierInfo.name}<br/>
            攻击力:${soldierInfo.attack}<br/>
            防御力:${soldierInfo.defense}<br/>
            最大生命:${soldierInfo.hp}<br/>
            技能:${soldierInfo.skill.map(item=>item[0]).join()}<br/>
            士兵数量:${soldierTem.soldierNum}<br/>
            招募代价:粮草x${soldierInfo.recruitFood}、银两x${soldierInfo.recruitGold}、时间x${soldierInfo.recruitTime}秒<br/>
            ${str}<br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //散部士兵挂出销售
    async sanbuSoldierSell(sid: string, cmd: number, userId: number, { soldierName }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'generalList', title: '挂出销售', service: this.serviceName, params: { soldierName } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        //设置返回
        let content = `请输入每个${soldierName}的销售价格：</br>
        <form action="${this.seturlOther(params, urlObj, '挂出销售', 'soldierSellPost', { thingId:soldierName })}" method="post">
        价格:<input name="count4" type="number" value="1" min="1"/><br/>
        <input name="submit" type="submit" value="挂出销售"/></form><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        await this.redis.hset('user' + userId, 'token', String(userId), 1800)
        return content
    }
    async soldierSellPost(userId: number, soldierName: string, count: number, cmd: number, sid: string) {
        count = Number(count);
        let msg = '', content = '';
        let res=await this.soldierNumEntity.update({userId,soldierName},{price:count,isSold:1})
        if(res.affected>0){
            content = `你已把${soldierName}挂牌销售，销售价格为${count}<br/>`
        }
        else{
            content = `未知错误，请稍后再试<br/>`
        }

        let routerInfo = await this.commonService.getPrevRouter1(userId)
        content += await this[routerInfo.name](sid, cmd, userId, routerInfo.params)
        return content
    }
    //补充士兵提交页 type:buchong 补充士兵 diaoli 调离士兵
    async buchongSoldier(sid: string, cmd: number, userId: number, { soldierId,type }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'generalList', title: '补充士兵', service: this.serviceName, params: { soldierId } } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content='';
        let soldierInfo=await this.soldierEntity.findOne({ where: { id: soldierId } })
        soldierId+='&'+type
        if(type=='diaoli'){
            content = `请输入你要调离士兵的数量(你目前可以调离${soldierInfo.count}名士兵)：</br>
            <form action="${this.seturlOther(params, urlObj, '调离士兵', 'buchongSoldierPost', { thingId:soldierId })}" method="post">
            调离数量:<input name="count5" type="number" value="1" min="1"/><br/>
            <input name="submit" type="submit" value="调离士兵"/></form><br/>`
        }else{
            //武将人口上限
            let soldierInfo=await this.soldierEntity.findOne({ where: { id: soldierId },relations:['general']})
            let maxSoldierNum=Math.floor((soldierInfo.general.countAll-soldierInfo.general.count)/soldierInfo.population)
            let soldierNum=await this.soldierNumEntity.findOne({ where: { userId, soldierType: 'bubing' },select:['soldierNum'] })
            let soldierNum1=0
            soldierNum&&(soldierNum1=soldierNum.soldierNum)
            content = `请输入你要补充士兵的数量(你目前可以补充${Math.min(maxSoldierNum,soldierNum1)}名士兵)：</br>
            <form action="${this.seturlOther(params, urlObj, '补充士兵', 'buchongSoldierPost', { thingId:soldierId })}" method="post">
            补充数量:<input name="count6" type="number" value="1" min="1"/><br/>
            <input name="submit" type="submit" value="补充士兵"/></form><br/>`
        }
        //设置返回
        content+=backRouter;
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        await this.redis.hset('user' + userId, 'token', String(userId), 1800)
        return content
    }
    //补充士兵提交 soldierId&buchong
    async buchongSoldierPost(userId: number, soldierStr: string, count: number, cmd: number, sid: string) {
        count = Math.abs(Number(count));
        let msg = '', content = '';
        let [soldierId,type]=soldierStr.split('&')
        
        let soldierInfo=await this.soldierEntity.findOne({ where: { id: Number(soldierId) },relations:['general']})
        if(type=='diaoli'){
            if(soldierInfo.count<count){
                content = `你调离的士兵数量超过了你拥有的士兵数量，请重新输入<br/>`
            }else{
                await this.dataSource.transaction(async (manager) => {
                    await manager.update(SoldierEntity,{id:Number(soldierId)},{count:soldierInfo.count-count})
                    let soldierNum=await manager.findOne(SoldierNum,{where:{userId,soldierName:soldierInfo.name}})
                    if(!soldierNum){
                        await manager.insert(SoldierNum,{userId,soldierName:soldierInfo.name,soldierNum:0})
                    }else{
                        await manager.update(SoldierNum,{userId,soldierName:soldierInfo.name},{soldierNum:()=>`soldierNum+${count}`})
                    }
                    content = `你已调离${soldierInfo.name} ${count}名士兵<br/>`
                })
            }
        }else{
           //补充士兵 不能大于武将人口上限
            let maxSoldierNum=Math.floor((soldierInfo.general.countAll-soldierInfo.general.count)/soldierInfo.population)
            let soldierNum=await this.soldierNumEntity.findOne({ where: { userId, soldierType: 'bubing' },select:['soldierNum'] })
            let soldierNum1=0
            soldierNum&&(soldierNum1=soldierNum.soldierNum) 
            if(soldierNum1<count){
                content = `你补充的士兵数量超过了你拥有的士兵数量，请重新输入<br/>`
            }else if(maxSoldierNum<count){
                content = `你补充的士兵数量超过了武将的最大带兵数量，请重新输入<br/>`
            }else{
                await this.dataSource.transaction(async (manager) => {
                    await manager.update(SoldierEntity,{id:Number(soldierId)},{count:()=>`count+${count}`})
                    if(count==soldierNum1){
                        await manager.delete(SoldierNum,{userId,soldierName:soldierInfo.name})
                    }else{
                        await manager.update(SoldierNum,{userId,soldierName:soldierInfo.name},{soldierNum:()=>`soldierNum-${count}`})
                    }
                    content = `你从散部调集了${count}名${soldierInfo.name}到${soldierInfo.general.name}部下<br/>`
                })
            }
        }
        await this.commonService.generalPopulation(soldierInfo.general.id)
        let routerInfo = await this.commonService.getPrevRouter1(userId)
        content += await this.generalService[routerInfo.name](sid, cmd, userId, routerInfo.params)
        return content
    }
    //遗忘原始兵种
    async forgetSoldier(sid: string, cmd: number, userId: number, { soldierId,type }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'forgetSoldier', title: '遗忘原始兵种', service: this.serviceName, params: { soldierId } } }
        let goodArr =await this.goodsService.getGoodsNum(userId, [47,48,49,50,51])
        let content=''
        if(goodArr.some(item=>item.count<2)){
            content='遗忘该兵种需要物品土之碎片x2，水之碎片x2，木之碎片x2，金之碎片x2，火之碎片x2，你没有足够的物品！<br/>'
        }else{
            if(type=='yiwang'){
                content='遗忘失败了</br>'
                let soldierInfo=await this.soldierEntity.findOne({ where: { id: Number(soldierId) },relations:['general']})
                await this.dataSource.transaction(async (manager) => {
                    //装备和坐骑删除待处理
                    await manager.delete(SoldierEntity,{id:Number(soldierId)})
                    let personEntityarr=await manager.find(PersonGoodsEntity,{where:{userId,good:{id:In([47,48,49,50,51])}}})
                    personEntityarr.forEach(item => {
                        item.count-=2
                    })
                    await manager.save(personEntityarr)
                    content=`土之碎片-2，水之碎片-2，木之碎片-2，金之碎片-2，火之碎片-2<br/>
                    ${soldierInfo.general.name}成功遗忘了招募${soldierInfo.name}兵种<br/>`
                    
                })
                return content+(await this.generalService.generalInfo(sid, cmd, String(userId), { generalId: soldierInfo.general.id }))
            }else{
                content = `遗忘原始兵种需要物品土之碎片x2，水之碎片x2，木之碎片x2，金之碎片x2，火之碎片x2<br/>
                <a href="${this.seturlOther(params, urlObj, '遗忘原始兵种', 'forgetSoldier', { soldierId,type:'yiwang' })}">确定遗忘</a><br/>`
            }
        }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        content +=backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //学习原始兵种
    async learnSoldier(sid: string, cmd: number, userId: number, { generalId,step=1,soldierType }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'learnSoldier', title: '学习原始兵种', service: this.serviceName, params: { generalId } } }
        let content=''
        let goodArr =await this.goodsService.getGoodsNum(userId, [47,48,49,50,51])
        if(goodArr.some(item=>item.count<2)){
            content='学习该兵种需要物品土之碎片x2，水之碎片x2，木之碎片x2，金之碎片x2，火之碎片x2，你没有足够的物品！<br/>'
        }else{
            
            if(step==1){
                let i=0;
                for (const key in soldierTemplate) {
                    if (Object.prototype.hasOwnProperty.call(soldierTemplate, key)) {
                        const element = soldierTemplate[key];
                        if(element.isInit){
                            i++
                            content+=`<a href="${this.seturlOther(params, urlObj, '学习原始兵种', 'learnSoldier', { generalId,step:2,soldierType:key })}">${i}.${element.name}</a><br/>`
                        }
                    }
                }
            }
            if(step==2){
                let soldierInfo=soldierTemplate[soldierType]
                content = `${soldierInfo.name}<br/>
                    ${soldierInfo.desc}<br/>
                    占用人口:${soldierInfo.population}<br/>
                    <a href="${this.seturlOther(params, urlObj, '学习原始兵种', 'learnSoldier', { generalId,step:3,soldierType })}">确定学习</a><br/>`
            }
            if(step==3){
                content='学习失败'
                await this.dataSource.transaction(async (manager) => {
                    let item=soldierTemplate[soldierType]
                    let soldier = new SoldierEntity()
                    soldier.name = item.name
                    soldier.attack = item.attack
                    soldier.hp = item.hp
                    soldier.defense = item.defense
                    soldier.population = item.population
                    soldier.userId = Number(userId)
                    soldier.general=generalId
                    soldier.skills=[]
                    //添加士兵技能
                    item.skill.forEach((item, index) => {
                        let skillSoldier = new SkillEntity()
                        skillSoldier.name = item[0]
                        skillSoldier.damage = item[1]
                        skillSoldier.distance = item[2]
                        skillSoldier.damageNum = item[3]
                        skillSoldier.weaponType = item[4]
                        if (item[5]) {
                            skillSoldier.interval = item[5]
                        }
                        if (item[0] == '普通攻击') {
                            skillSoldier.isDefault = 1
                        }
                        skillSoldier.soldier = soldier
                        soldier.skills.push(skillSoldier)
                    })
                    await manager.save(SoldierEntity, soldier)
                    let personEntityarr=await manager.find(PersonGoodsEntity,{where:{userId,good:{id:In([47,48,49,50,51])}}})
                    personEntityarr.forEach(item => {
                        item.count-=2
                    })
                    await manager.save(personEntityarr)
                    content=`学习成功，你已获得${soldierTemplate[soldierType].name}兵种<br/>`

                })
                return content+(await this.generalService.generalInfo(sid, cmd, String(userId), { generalId }))
            }
        }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        content +=backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //调整武将顺序
    async adjustGeneral(sid: string, cmd: number, userId: number, { generalId }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'adjustGeneral', title: '设置顺序', service: this.serviceName, params: { generalId } } }
        let generalInfo=await this.generalEntity.findOne({ where: { id: Number(generalId) }})
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `请输入你要调整的武将顺序：</br>
        <form action="${this.seturlOther(params, urlObj, '调整武将顺序', 'adjustGeneralPost', { thingId:generalId })}" method="post">
        武将顺序:<input name="count7" type="number" value="${generalInfo.sort}" min="1"/><br/>
        <input name="submit" type="submit" value="调整武将顺序"/></form><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        await this.redis.hset('user' + userId, 'token', String(userId), 1800)
        return content
    }
    //调整武将顺序提交
    async adjustGeneralPost(userId: number, generalId: string, count: number, cmd: number, sid: string) {
        count = Math.abs(Number(count));
        let content='';
        let res=await this.generalEntity.update({id:Number(generalId)},{sort:count})
        if(res.affected>0){
            content = `你已调整武将顺序为${count}<br/>`
        }else{
            content = `未知错误，请稍后再试<br/>`
        }
        let routerInfo = await this.commonService.getPrevRouter1(userId)
        content += await this.generalService[routerInfo.name](sid, cmd, userId, routerInfo.params)
        return content
    }
    //祝福列表
    async blessingList(sid: string, cmd: number, userId: number, {type,id }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'blessingList', title: '祝福列表', service: this.serviceName, params: { type,id } } }
        let params1:any={status:1};
        let arr=['攻击秘术状态','防御秘术状态','生命储备']
        switch(type){
            case 'general':
                params1.generalId=id
                break;
            case 'soldier':
                params1.soldierId=id
                break;
            case 'user':
                arr=['','','生命储备','VIP月卡状态','四倍潜能卡状态','双倍潜能卡状态','双倍银两卡状态','四倍资源卡状态','双倍资源卡状态']
                params1.userId=id
                break;
            default:
                break;
        }
        let blessList=await this.blessEntity.find({ where: params1 })
        
        let str=''
        arr.forEach((item, index) => {
            if(item){
                let res=blessList.find(item1=>item1.blessType==index)
                if(res){
                    str+=`${item}:生效，还有${formatDuring(res.endTime.getTime()-new Date().getTime())}失效<br/>`
                }else{
                    str+=`${item}:无效<br/>`
                }
            }
            
        })
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `${str}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    
    //坐骑详情
    async mountDetail(sid: string, cmd: number, userId: number, { manorId,mountId }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'mountDetail', title: '坐骑详情', service: this.serviceName, params: { mountId } } }
        let mountInfo=await this.mountEntity.findOne({ where: { id: Number(mountId) }})
        let equipNameStr=''
        for (const key in mountEquip) {
            let item=mountEquip[key]
            if(mountInfo[item.id]){
                equipNameStr+=mountInfo[item.title]+','
            }
        }
        let info=mountConfig[mountInfo.mountType]
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `${info.name}<br/>
            等级:100 [<a href="">升级</a>]<br/>
            增加攻击:${mountInfo.attack}%<br/>
            增加防御:${mountInfo.defense}%<br/>
            装备:${equipNameStr}<br/>
            状态：${mountInfo.status===1?'空闲':'外出'}<br/>
            ${mountInfo.sellStatus===2?`挂售价格:${mountInfo.price}<br/>`:''}
            <a href="${this.seturlOther(params, urlObj, '坐骑装备', 'mountEquip', { mountId })}">装备</a><br/>
            ${(manorId&&mountInfo.status==1&&mountInfo.sellStatus==1)?
                `<a href="${this.seturlOther(params, urlObj, '确认', 'confirmPage', { msg:'你确定要赶走'+info.name+'吗？',pageName:'buildInfo',serviceName:'manor', other:{manorId,eventType:2,eventId:mountId} })}">赶走</a><br/>`
                :''
            }
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //坐骑装备页面
    async mountEquip(sid: string, cmd: number, userId: number, { mountId,equipId=null,removeEquipId=null }) {
        let mountInfo=await this.mountEntity.findOne({ where: { id: Number(mountId) }})
        if(mountInfo.userId!=userId){
            return await this.otherSeaService.mountEquip(sid, cmd, userId, { mountId })
        }
        let msg=''
        if(equipId){//装备-1
            let goodInfo=await this.personGoodsEntity.findOne({ where: { id:equipId} ,relations:['good'] })
            let title=mountEquip[goodInfo.good.subType].title
            let id=mountEquip[goodInfo.good.subType].id
            await this.dataSource.transaction(async (manager) => {
                await manager.update(MountEntity,{id:Number(mountId)},{[title]:goodInfo.good.name,[id]:goodInfo.id} )
                goodInfo.count-=1
                goodInfo.usedNum+=1
                await manager.save(goodInfo)
            })
        }
        if(removeEquipId){
            let goodInfo=await this.personGoodsEntity.findOne({ where: { id:removeEquipId} ,relations:['good'] })
            let title=mountEquip[goodInfo.good.subType].title
            let id=mountEquip[goodInfo.good.subType].id
            await this.dataSource.transaction(async (manager) => {
                await manager.update(MountEntity,{id:Number(mountId)},{[title]:null,[id]:null} )
                goodInfo.count+=1
                goodInfo.usedNum-=1
                await manager.save(goodInfo)
            })
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'mountEquip', title: '坐骑装备', service: this.serviceName, params: { mountId } } }
        
        let info=mountConfig[mountInfo.mountType]
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let contentStr=''
        Object.keys(mountEquip).forEach(item=>{
            let element=mountEquip[item]
            contentStr+=`${element.name}:`
            if(mountInfo[element.id]){
                contentStr+=`<a href="${this.seturlOther(params, urlObj, '装备详情', 'wearingEquip', { id:mountInfo[element.id] },'general')}">${mountInfo[element.title]}</a> 
                <a href="${this.seturlOther(params, urlObj, '卸下', 'mountEquip', { mountId,removeEquipId:mountInfo[element.id] })}">卸下</a>`
            }else{
                contentStr+=`<a href="${this.seturlOther(params, urlObj, '选择装备', 'checkEquip', { type:6,subType:item, itemId: mountInfo.id },'generalother')}">选择</a>`
            }
            contentStr+='<br/>'
        })
        let content = `${msg?msg+'<br/>':''}
            ${info.name}的装备<br/>
            ${contentStr}
            <a href="">自动换装</a><br/>
            <a href="">修理所有</a><br/>
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //选择装备页面
    async checkEquip(sid: string, cmd: number, userId: number, { type,subType, page = 1,itemId,title='选择装备' }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'checkEquip', title, service: this.serviceName, params: { type,subType,page } } }
        //获取物品列表
        let [goodsList, totalItems] = await this.personGoodsEntity.findAndCount({
            where: { userId: Number(userId), count: MoreThan(0), good: { type, subType } },
            relations: ['good'],
            take: this.size, skip: (page - 1) * this.size
        })
        let contentStr = title+'<br/>';
        let routerList = JSON.parse(await this.redis.hget('user' + userId, 'routerList'))
        let routerInfo = routerList[routerList.length - 2]
        goodsList.length==0&&(contentStr+='当前没有可选择的装备<br/>')
        goodsList.forEach((item, index) => {
            let params1:any={equipId: item.id }
            switch(true){
                case [2,3].includes(type):
                    params1.generalId=itemId
                    break;
                case [4,5].includes(type):
                    params1.soldierId=itemId
                    break;
                case [6].includes(type):
                    params1.mountId=itemId
                    break;
                case [7].includes(type):
                    params1.thingId=itemId
                    params1.type='xiangbaoshi'
                    break;
                default:
                    params1.mountId=itemId
                    break;
            }
            //使用完物品返回上级路由
            contentStr += `<a href="${this.seturlOther(params, urlObj, routerInfo.title, routerInfo.name, params1,routerInfo.service)}">${index + 1}.${item.good.name}${item.good.bind==1?'[绑]':''}x${item.count}</a></br>`
        })
        //分页
        let pageStr = this.setpage(params, urlObj, page, totalItems, { type,subType,page }, 'checkEquip', title)
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `${contentStr}${pageStr}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //装备所有人变更  背包到装备和装备到背包
    //type 1 背包到装备 2 装备到背包 typeid 装备id
    //  itemId 对应id
    async equipChange(type: number,typeId: number,itemId:number) {
        let msg='操作失败'
        if(type===1){
            //背包到装备
            let personGood=await this.personGoodsEntity.findOne({ where: { id: typeId },relations:['good'] })
            if(!personGood){
                return '物品不存在'
            }
            let generalEquip=new GeneralEquip();
            generalEquip.equipmentId=personGood.good.id;
            [2,3].includes(personGood.good.type)&&(generalEquip.generalId=itemId);
            [4,5].includes(personGood.good.type)&&(generalEquip.soldierId=itemId);
            personGood.good.type===6&&(generalEquip.mountId=itemId);
            generalEquip.type=personGood.good.subType;
            personGood.attack&&(generalEquip.attack=personGood.attack)
            personGood.defense&&(generalEquip.defense=personGood.defense)
            personGood.hp&&(generalEquip.hp=personGood.hp)
            personGood.durability&&(generalEquip.durability=personGood.durability)
            personGood.isBind&&(generalEquip.isBind=personGood.isBind);
            await this.dataSource.transaction(async (manager) => {
                let res=await manager.insert(GeneralEquip, generalEquip)
                if(res.identifiers.length>0){
                    let equipId=res.identifiers[0].id
                    await manager.update(PersonGoodsHoleEntity,{personGoodId:personGood.id},{equipId:equipId})
                }
                personGood.count--
                await manager.save(PersonGoodsEntity, personGood)
                msg='装备成功'
            })
        }else{
            //装备到背包
            let generalEquip=await this.generalEquipEntity.findOne({ where: { id: typeId } })
            if(!generalEquip){
                return '物品不存在'
            }
            let personGood=new PersonGoodsEntity()
            personGood.userId=itemId
            personGood.count=1
            personGood.attack=generalEquip.attack
            personGood.defense=generalEquip.defense
            personGood.hp=generalEquip.hp
            personGood.durability=generalEquip.durability
            personGood.isBind=generalEquip.isBind;
            let goodInfo=await this.goodsService.getGoodDetail(generalEquip.equipmentId)
            personGood.good=goodInfo
            await this.dataSource.transaction(async (manager) => {
                if(goodInfo.isStack==2){
                    let params1:any={
                        userId:itemId,
                        good:{id:goodInfo.id}
                    }
                    generalEquip.isBind&&(params1.isBind=generalEquip.isBind)
                    let personGoodInfo=await manager.findOne(PersonGoodsEntity,{where:params1})
                    if(personGoodInfo){
                        personGoodInfo.count++
                        await manager.save(PersonGoodsEntity, personGoodInfo)
                    }else{
                        await manager.insert(PersonGoodsEntity, personGood)
                    }
                }else{
                    let res=await manager.insert(PersonGoodsEntity, personGood)
                    if(res.identifiers.length>0){
                        let personGoodId=res.identifiers[0].id
                        await manager.update(PersonGoodsHoleEntity, { goodId: typeId }, { equipId: personGoodId })
                    }
                }
                await manager.delete(GeneralEquip, { id: typeId })
                msg='卸下成功'
            })
        }
        return msg+'<br/>'
    }
    //选择坐骑 type 1武将 2士兵
    async checkMount(sid: string, cmd: number, userId: number, { type,page = 1,itemId,mountId }) {
        let msg=''
        if(mountId){
            let itemInfo=
                type==1?
                    (await this.generalEntity.findOneBy({ id: Number(itemId) }))
                    :(await this.soldierEntity.findOneBy({ id: Number(itemId) }))
            let mountInfo=await this.mountEntity.findOneBy({ id: Number(mountId) })
            if(itemInfo.level<mountInfo.level){
                msg= '等级不足，无法骑上该坐骑<br/>'
            }else{
                let params1:any={status:2}
                type===1?params1.generalId=itemId:params1.soldierId=itemId
                await this.mountEntity.update({id:mountId},params1)
                if(type===1){
                    return await this.generalService.generalInfo(sid, cmd, String(userId), { generalId: itemId })
                }else{
                    return await this.generalService.soldierInfo(sid, cmd, String(userId), { soldierId: itemId })
                }
            }
            
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'checkMount', title: '选择坐骑', service: this.serviceName, params: { type,itemId,page } } }
        //获取物品列表
        let [goodsList, totalItems] = await this.mountEntity.findAndCount({
            where: { userId: Number(userId), status: 1,sellStatus:1 },
            take: this.size, skip: (page - 1) * this.size
        })
        let contentStr = `${msg}选择坐骑:<br/>`;
        if(goodsList.length==0){
            contentStr+='当前没有可选择的坐骑<br/>'
        }else{
            goodsList.forEach((item, index) => {
                let name=mountConfig[item.mountType].name
                contentStr += `<a href="${this.seturlOther(params, urlObj, '选择坐骑', 'checkMount', {type,page,itemId,mountId:item.id})}">${index + 1}.${name}(${item.level}级)</a></br>`
            })
        }
        //分页
        let pageStr = this.setpage(params, urlObj, page, totalItems, { type,itemId,page }, 'checkMount', '选择坐骑')
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `${contentStr}${pageStr}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //无字天书研究页面
    async researchBook(sid: string, cmd: number, userId: string, {}) {
        let pageTitle='无字天书研究'
        // const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle) 
        // let content = `无字天书<br/>
        //     研究时间:${bookInfo.time}秒<br/>
        //     <a href="${this.seturlOther(params, urlObj, '研究', 'researchBookPost', { bookId })}">确定研究</a><br/>`
        // content +=backRouter
        // await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        // return content
    }
    //战场设置
    async battleSetting(sid: string, cmd: number, userId: string) {
        let configInfo:any=await this.redis.hget('user' + userId, 'config')
        if(!configInfo){
            configInfo=await this.configEntity.findOneBy({userId:Equal(Number(userId))})
        }else{
            configInfo=JSON.parse(configInfo)
        }
        let pageTitle='战场设置'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle) 
        let content = `战场设置<br/>
        <form action="${this.seturlOther(params, urlObj, '战场设置', 'battleSetting', {thingId:userId})}" method="post">
        旗号：<input name="count10[flag]" type="text" value="${configInfo?.flag}" maxlength="3"/><br/>
        自动捡起物品：<select name="count10[autoPick]">
        <option value="1" ${configInfo?.autoPick==1?'selected':''}>打开</option>
        <option value="2" ${configInfo?.autoPick==2?'selected':''}>关闭</option>
        </select><br/>
        PK状态：<select name="count10[pkStatus]">
        <option value="1" ${configInfo?.pkStatus==1?'selected':''}>打开</option>
        <option value="2" ${configInfo?.pkStatus==2?'selected':''}>关闭</option>
        </select><br/>
        是否默认允许第三方加入战场：<select name="count10[allowJoin]">
        <option value="1" ${configInfo?.allowJoin==1?'selected':''}>允许</option>
        <option value="2" ${configInfo?.allowJoin==2?'selected':''}>禁止</option>
        </select><br/>
        战场自动开打秒数(大于等于30,小于等于120)：<input name="count10[autoAttack]" type="text" value="${configInfo?.autoAttack}" format="*N" style="-wap-input-format:*N" maxlength="3"/><br/>
        <input name="submit" type="submit" title="设置" value="设置"/></form><br/>`
        content +=backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content
    }
    //个性设置
    async personalSetting(sid: string, cmd: number, userId: string) {
        let configInfo:any=this.userConfigProvider.getUserSetting()
        let pageTitle='个性设置'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle) 
        let content = `个性设置<br/>
        <form action="${this.seturlOther(params, urlObj, '战场设置', 'battleSettingPost', {thingId:userId})}" method="post">
        公共信息：<select name="count10[publicInfo]" value="${configInfo?.publicInfo}">
        <option value="1" ${configInfo?.publicInfo==1?'selected':''}>打开</option>
        <option value="2" ${configInfo?.publicInfo==2?'selected':''}>关闭</option>
        </select><br/>
        帮派信息：<select name="count10[guildInfo]" value="${configInfo?.guildInfo}">
        <option value="1" ${configInfo?.guildInfo==1?'selected':''}>打开</option>
        <option value="2" ${configInfo?.guildInfo==2?'selected':''}>关闭</option>
        </select><br/>
        队伍信息：<select name="count10[teamInfo]" value="${configInfo?.teamInfo}">
        <option value="1" ${configInfo?.teamInfo==1?'selected':''}>打开</option>
        <option value="2" ${configInfo?.teamInfo==2?'selected':''}>关闭</option>
        </select><br/>
        私聊信息：<select name="count10[privateInfo]" value="${configInfo?.privateInfo}">
        <option value="1" ${configInfo?.privateInfo==1?'selected':''}>打开</option>
        <option value="2" ${configInfo?.privateInfo==2?'selected':''}>关闭</option>
        </select><br/>
        是否接受赠送：<select name="count10[acceptGift]" value="${configInfo?.acceptGift}">
        <option value="1" ${configInfo?.acceptGift==1?'selected':''}>打开</option>
        <option value="2" ${configInfo?.acceptGift==2?'selected':''}>关闭</option>
        </select><br/>
        分页列表行数(3~20)：<input name="count10[pageLine]" type="text" value="${configInfo?.pageLine}" format="*N" style="-wap-input-format:*N" maxlength="2"/><br/>
        <input name="submit" type="submit" title="设置" value="设置"/></form><br/>`
        content +=backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content
    }
    //战场设置提交
    async battleSettingPost(userId: number, attrItem: string, attr: any, cmd: number, sid: string) {
        let msg=''
        let configInfo=await this.configEntity.findOneBy({userId:Equal(userId)})
        !configInfo&&(configInfo=new ConfigEntity())
        configInfo.userId=userId
        if('flag' in attr){
            if(!attr.flag){
                msg='旗号不能为空'
            }
            if(attr.flag.length>3){
                msg='旗号不能超过3个字符'
            }
            if(attr.autoAttack<30||attr.autoAttack>120){
                msg='战场自动开打秒数必须在30~120之间'
            }
        }
        if('publicInfo' in attr){
            if(attr.pageLine<3||attr.pageLine>20){
                msg='分页列表行数必须在3~20之间'
            }
        }
        for(let item in attr){
            configInfo[item]=attr[item]
        }
        if(!msg){
            let res=await this.configEntity.save(configInfo)
            if(res){
                await this.redis.hdel('user'+userId,'userSetting')
                msg='设置成功'
            }else{
                msg='设置失败'
            }
        }
        
        let content='';
        if('flag' in attr){
            content=await this.battleSetting(sid, cmd, String(userId))
        }
        if('publicInfo' in attr){
            content=await this.personalSetting(sid, cmd, String(userId))
        }

        return msg+'<br/>'+content
    }
    //查找玩家
    async searchPlayer(sid: string, cmd: number, userId: string, {playerName=''}) {
        let contentStr=''
        let pageTitle='个性设置'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle) 
        if(playerName){
            let playerInfo=await this.roleEntity.find({where:{name:Like(`%${playerName}%`)}})
            if(playerInfo){
                //根据redis 判断是否在线
                for(let i=0;i<playerInfo.length;i++){
                    let item=playerInfo[i]
                    let isOnline=await this.redis.hget('user'+item.id,'token')
                    if(isOnline){
                        contentStr+=`<a href="${this.seturlOther(params, urlObj, '玩家信息', 'myStatus', {targetUserId:item.id},'gcmd')}">${i+1}.${item.name}[在线]</a><br/>`
                    }else{
                        contentStr+=`${i+1}.${item.name}[离线]<br/>`
                    }
                }
            }
        }
        let content = `${contentStr}[查找玩家]<br/><form action="${this.seturlOther(params, urlObj, '查找玩家', 'searchPlayer', {playerName})}" method="post">
            请输入查找目标：
            <input name="count11" type="text" maxlength="30" value="${playerName}" />
            <input name="submit" type="submit" title="查找" value="查找"/></form><br/>`
        content +=backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content
    }
    //查找玩家提交
    async searchPlayerPost(userId: number, attrItem: string, playerName: any, cmd: number, sid: string) {
        return await this.searchPlayer(sid, cmd, String(userId), {playerName})
    }
    //升多级提交
    async levelUpPost(userId: number, attrItem: string, attr: any, cmd: number, sid: string) {
        let msg=''
        let goodNum=await this.goodsService.getGoodNumByName(Number(userId),'升级牌')
        if(goodNum<1){
            msg='你没有升级牌'
        }
        if(attr.level<1){
            msg='请输入要升的级数'
        }
        if(msg){
            let routerList=JSON.parse(await this.redis.hget('user'+userId,'routerList'))
            let router=routerList.pop()
            return await this[router.service][router.name](sid, cmd, String(userId), router.params)
        }
    }

    //排行榜列表
    async rankList(sid: string, cmd: number, userId: string, {type}) {
        let pageTitle='排行榜列表'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle) 
        let content = `排行榜<br/>
        1.<a href="${this.seturlOther(params, urlObj, '名将排行榜', 'rankList', {type:1},'gcmd')}">名将排行榜</a><br/>
        <br/>
        2.<a href="${this.seturlOther(params, urlObj, '武将最大带兵', 'rankList', {type:2},'gcmd')}">武将最大带兵</a><br/>
        <br/>
        3.<a href="${this.seturlOther(params, urlObj, '财富榜', 'rankList', {type:3},'gcmd')}">财富榜</a><br/>
        <br/>
        4.<a href="${this.seturlOther(params, urlObj, '武将防御榜', 'rankList', {type:4},'gcmd')}">武将防御榜</a><br/>
        <br/>
        5.<a href="${this.seturlOther(params, urlObj, '士兵攻击榜', 'rankList', {type:5},'gcmd')}">士兵攻击榜</a><br/>
        <br/>
        6.<a href="${this.seturlOther(params, urlObj, '士兵防御榜', 'rankList', {type:6},'gcmd')}">士兵防御榜</a><br/>
        <br/>
        7.<a href="${this.seturlOther(params, urlObj, '领土数量榜', 'rankList', {type:7},'gcmd')}">领土数量榜</a><br/>
        <br/>
        8.<a href="${this.seturlOther(params, urlObj, '坐骑排行榜', 'rankList', {type:8},'gcmd')}">坐骑排行榜</a><br/>
        <br/>
        10.<a href="${this.seturlOther(params, urlObj, '武将体力榜', 'rankList', {type:10},'gcmd')}">武将体力榜</a><br/>
        <br/>
        11.<a href="${this.seturlOther(params, urlObj, '士兵体力榜', 'rankList', {type:11},'gcmd')}">士兵体力榜</a><br/>
        <br/>
        12.<a href="${this.seturlOther(params, urlObj, '武将五行相生', 'rankList', {type:12},'gcmd')}">武将五行相生</a><br/>
        <br/>
        13.<a href="${this.seturlOther(params, urlObj, '寿星榜', 'rankList', {type:13},'gcmd')}">寿星榜</a><br/>
        <br/>
        14.<a href="${this.seturlOther(params, urlObj, '挑战大赛积分', 'rankList', {type:14},'gcmd')}">挑战大赛积分</a><br/>
        <br/>
        15.<a href="${this.seturlOther(params, urlObj, '深渊副本排行榜', 'rankList', {type:15},'gcmd')}">深渊副本排行榜</a><br/>
        <br/>`
        content +=backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        return content
    }
    //排行榜详情
    async rankDetail(sid: string, cmd: number, userId: string, {type,page=1}) {
        let pageTitle='',contentStr='';
        if(type==1){
            pageTitle='名将排行榜'
            let generalList=await this.generalEntity.find({where:{},order:{attack:'DESC'},take:20,skip:(page-1)*20})
            for(let i=0;i<generalList.length;i++){
                let item=generalList[i]
                contentStr+=`${i+1}.${item.name}[${item.attack}]<br/>`
            }
        }
        if(type==2){
            pageTitle='武将最大带兵'
        }
        if(type==3){
            pageTitle='财富榜'
        }
        if(type==4){
            pageTitle='武将防御榜'
        }
        if(type==5){
            pageTitle='士兵攻击榜'
        }
        if(type==6){
            pageTitle='士兵防御榜'
        }
        if(type==7){
            pageTitle='领土数量榜'
        }
        if(type==8){
            pageTitle='坐骑排行榜'
        }
        if(type==10){
            pageTitle='武将体力榜'
        }
        if(type==11){
            pageTitle='士兵体力榜'
        }
        if(type==12){
            pageTitle='武将五行相生'
        }
        if(type==13){
            pageTitle='寿星榜'
        }
        if(type==14){
            pageTitle='挑战大赛积分'
        }
        if(type==15){
            pageTitle='深渊副本排行榜'
        }
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle) 
        let content = pageTitle+`<br/>`
        content +=backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        return content
    }
    //npc设置页面
    async npcSetting(userId: number,itmeName:string) {
        
    }
    //模板
    async moban(sid: string, cmd: number, userId: string) {
        let eventList: ManorEventEntity[] = await this.manorEventEntity.find()
        console.log('eventList', eventList);
        // const params = {sid, cmd}
        // const urlObj = { [cmd]: { name: 'home', title: '游戏首页',service:'general', params:{} } }
        // let backRouter = await this.backRouter(userId, params, urlObj)
        // let content = ``
        // await this.redis.hset('user'+userId,'routers', JSON.stringify(urlObj))
        // await this.redis.expire('user'+userId, 1800); 
        // return content;
        return '模板'
    }
    //设置返回路由
    async backRouter(userId, params, urlObj) {
        //删除重复的路由
        function removeDuplicates(arr) {
            if (!arr || arr.length <= 1) return arr;
            const firstName = arr[0];
            let lastIndex = -1;
            for (let i = arr.length - 1; i >= 0; i--) {
                if (arr[i].service === firstName.service&&arr[i].name === firstName.name) {
                    lastIndex = i;
                    break;
                }
            }
            if (lastIndex > 0) {
                return arr.slice(lastIndex);
            }
            return arr;
        }
        let str = ''
        //返回路由
        let routerList = JSON.parse(await this.redis.hget('user' + userId, 'routerList'))
        routerList.reverse().shift()
        routerList=removeDuplicates(routerList)
        routerList.forEach(element => {
            if (!element.params.hidden) {
                str += `<a href="${this.seturlOther(params, urlObj, element.title, element.name, element.params, element.service)}">返回${element.title}</a><br/>`
            }
        });
        return str
    }
    /**
     * 
     * @param params cnd sid参数 
     * @param urlObj url集合
     * @param title 页面标题
     * @param name 页面名称
     * @param param 请求参数
     * @param service 页面服务名
     * @returns string 页面url
}
     */
    seturlOther(params, urlObj, title: string, name: string, attr = {}, service = this.serviceName) {
        params.cmd++
        urlObj[params.cmd] = { title, name, service, params: attr }
        return `/gcmd?sid=${params.sid}&cmd=${params.cmd}`
    }
    //中间确认页 提示内容 页面 服务
    async confirmPage(sid: string, cmd: number, userId: string, { msg,pageName,serviceName,other}) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'confirmPage', title: '提示', service: this.serviceName, params: { msg,pageName,serviceName,other } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content =`
        ${msg}<br/>
        <a href="${this.seturlOther(params, urlObj, '确定', pageName, other, serviceName)}">确定</a><br/>
        ${backRouter}
        `
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //中间页 提示
    async getMiddlePage(sid: string, cmd: number, userId: string, { msg }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'getMiddlePage', title: '提示', service: 'general', params: { hidden: true } } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = msg + '<br/>' + backRouter
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    setpage(params, urlObj, page, totalItems, attrs, router, routerName?: string) {
        let pageStr = '';
        let totalPages = Math.ceil(totalItems / this.size);
        const createPageLink = (newPage, text) => {
            let newAttrs = { ...attrs, page: newPage };
            pageStr += `<a href="${this.seturlOther(params, urlObj, routerName, router, newAttrs)}">${text}</a> `;
        };
        if (page > 2) createPageLink(1, '首页');
        if (page !== 1) createPageLink(page - 1, '上一页');
        if (page < totalPages) createPageLink(page + 1, '下一页');
        if (page < totalPages - 1 && totalPages > 1) createPageLink(totalPages, '末页');
        return pageStr
    }
    
    //清除副本记录  不传dungeonName 清理所有副本记录
    @OnEvent('clearDungeon')
    async clearDungeon(userId: string,dungeonName:string) {
        //获取角色队伍id    
        let teamRole=await this.teamRolesEntity.findOne({where:{userId:Number(userId)},relations:['team']})        
        let dungeonArr=await this.dungeonLogEntity.find({where:{userId:Number(userId),status:1}})
        if(dungeonName){
            dungeonArr=dungeonArr.filter(item=>item.name==dungeonName)
        }
        if(dungeonArr.length){
            for(let i=0;i<dungeonArr.length;i++){
                let dungeon=dungeonArr[i]
                let npcArr=await this.npcEntity.find({where:{dungeonName:dungeon.name},select:['id']})
                for(let j=0;j<npcArr.length;j++){
                    let npc=npcArr[j]
                    await this.redis.delstr('t'+npc.id+'u'+teamRole.team.id)
                }
            }
            //清理副本记录
            dungeonArr.forEach(item=>{
                item.status=2
            })
            await this.dungeonLogEntity.save(dungeonArr)
            //移动玩家
            let npcInfo=await this.npcEntity.findOne({where:{dungeonName:dungeonName}})
            if(npcInfo){
                let userInfo=await this.roleEntity.findOne({where:{id:Number(userId)}})
                userInfo.gpsId=npcInfo.mapId
                await this.roleEntity.save(userInfo)
                await this.redis.hset('user'+userId,'userInfo',JSON.stringify(userInfo))
            }
        }
        
    }
    
    //第三方页面-入梦
    async dream(sid: string, cmd: number, userId: string) {
        let pageTitle='入梦'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle,'dream') 
        let content = `怨魂复活报复人间引发惊世浩劫，生灵涂炭英雄集结寻找神秘救赎。上古神兵再现人间唯一救命稻草，劫后重生是福是祸全凭自己掌握。<br/>
            <a href="${this.seturlOther(params, urlObj, '入梦', 'main', {changeGps:10411},'gcmd')}">入梦(破碎梦晶石x1)</a><br/>
            <a href="${this.seturlOther(params, urlObj, '挑战上古怨魂', 'main', {changeGps:10411},'gcmd')}">挑战上古怨魂</a><br/>`
        content +=backRouter
        return content
    }
    //
    async formInit(sid: string, cmd: number, userId: string, { obj }) {
        if(!obj.type)obj.type='number'
        if(!obj.buttonName)obj.buttonName='确定'
        if(!obj.msg)obj.msg=''
        let {backRouter,params,urlObj} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {thingId:obj,obj},'提交','formInit')
        let content = `${obj.msg&&(obj.msg+'<br/>')}${obj.title}：<br/>
        <form action="${this.seturlOther(params, urlObj,'提交', 'sellGoodGuaPost', { thingId:obj })}" method="post">
        ${obj.label}: <input name="count13" type="${obj.type}" value="" maxlength="10"/><br/>
        <input type="submit" value="${obj.buttonName}"/></form>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content;
    }
    async formSubmit(userId:number,attrItem: any, attr: any, cmd: number,sid: string) {
        //修改名称
        if(attrItem.label=='新角色名称'){
            let msg=''
            if(attr.length<3||attr.length>6){
                attrItem.msg='角色名长度必须在3~6之间'
                return await this.formInit(sid, cmd, String(userId), { obj: attrItem })
            }
            let personGood=await this.goodsService.getGoodEntityByName(Number(userId),'VIP月卡')
            if(!personGood||personGood.count<1){
                attrItem.msg='改名需要VIP月卡x1'
                return await this.formInit(sid, cmd, String(userId), { obj: attrItem })
            }
            await this.dataSource.transaction(async (manager) => {
                await manager.update(RoleEntity, { id: Number(userId) }, { name: attr })
                personGood.count-=1
                await manager.save(PersonGoodsEntity,personGood)
            })
            return 'VIP月卡-1，角色名修改成功<br/>'+await this.gcmd.myStatus(sid, cmd, String(userId),{})
        }
    }
}
