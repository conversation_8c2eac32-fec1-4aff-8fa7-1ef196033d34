import { IsNumber, IsOptional, IsString } from 'class-validator';

export class GoodAdminDto {
    @IsNumber()
    @IsOptional()
    id: number;

    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    desc: string;

    @IsNumber()
    @IsOptional()
    price: number;

    @IsNumber()
    type: number;

    @IsNumber()
    @IsOptional()
    subType: number;

    @IsNumber()
    @IsOptional()
    addhp: number;

    @IsNumber()
    @IsOptional()
    addatk: number;

    @IsNumber()
    @IsOptional()
    adddef: number;

    @IsNumber()
    @IsOptional()
    bind: number;

    @IsNumber()
    @IsOptional()
    weight: number;

    @IsNumber()
    @IsOptional()
    useLevel: number;

    @IsNumber()
    @IsOptional()
    roleUse: number;

    @IsNumber()
    @IsOptional()
    generalUse: number;

    @IsNumber()
    @IsOptional()
    soldierUse: number;

    @IsNumber()
    @IsOptional()
    fightUse: number;

    @IsNumber()
    @IsOptional()
    isStack: number;
}