import { Injectable } from '@nestjs/common';
import { Cron, SchedulerRegistry } from '@nestjs/schedule';

import { <PERSON>ronJob } from 'cron';
@Injectable()
export class TasksService {
    constructor(private schedulerRegistry: SchedulerRegistry) {}
    
    // @Cron('45 * * * * *')
    // handleCron() {
    //     // console.log('该方法将在45秒标记处每分钟运行一次');
    // }

    // @Interval(10000)
    // handleInterval() {
    //     this.logger.debug('2');
    // }

    // @Timeout(5000)
    // handleTimeout() {
    //     this.logger.debug('3');
    // }
    //创建定时任务
    /**
     * 
     * @param timer 执行时间
     * @param name 任务名称
     * @param callback 回调函数
     * @returns 
     */
    async createTimer(timer,name,callback){
        console.log('');
        //判断当前任务名是否存在 存在就不添加
        const timeouts = this.schedulerRegistry.getTimeouts();
        if(timeouts.includes(name)) return console.log('任务名已存在');
        const job:any = new CronJob(timer, callback)
        // 动态添加定时任务
        this.schedulerRegistry.addCronJob(name,job);
        job.start();
    }
    //创建间隔任务
    async createInterval(interval,name,callback){
        const timeouts = this.schedulerRegistry.getIntervals();
        console.log('timeouts', timeouts);
        if(timeouts.includes(name)) return console.log('任务名已存在');
        const job = setInterval(callback, interval); 
        // 动态添加定时任务 
        this.schedulerRegistry.addInterval(name,job); 
    }
    //清理间隔任务
    async clearInterval(name){
        
        const timeouts = this.schedulerRegistry.getIntervals();
        if(timeouts.includes(name)){
            const job = this.schedulerRegistry.getInterval(name);
            clearInterval(job);
            this.schedulerRegistry.deleteInterval(name);
        }
    }
    //获取任务列表
    async getTaskList(){
        const timeouts = this.schedulerRegistry.getIntervals();
        console.log(timeouts);
    }
}