//祝福状态
const blessConfig = [
    {
        name: '攻击秘术',
        desc:'武将或者士兵使用后，三小时内其杀伤力立刻提升30%'
    },
    {
        name: '防御秘术',
        desc:'武将或者士兵使用后，三小时内其防御力立刻提升30%'
    },
    {
        name: '自动补血丹',
        desc:'使用后，自动增加120万生命储备，玩家的武将、兵种在战斗中失血，立刻为其自动补满，直到这120万的生命储备用完。'
    },
]
//坐骑模板
const mountConfig = {
    wuqing:{name: '乌青战马',attack:0.1,defense:0.1},   
    qianli:{name: '千里良驹',attack:0.2,defense:0.2},   
    hanxue:{name: '汗血宝马',attack:0.2,defense:0.3},   
    chitu:{name: '赤兔宝马',attack:0.3,defense:0.2},   
    lieyan:{name: '烈炎狂豹',attack:0.3,defense:0.3},   
    qixin:{name: '七心战虎',attack:0.3,defense:0.35},   
    tiannu:{name: '天怒神兽',attack:0.35,defense:0.3},   
    mengyan:{name: '梦魇灵兽',attack:0.35,defense:0.35},   
    wucai:{name: '五彩神牛',attack:0.45,defense:0.4,desc:'五彩神牛象征了爱人之间幸福的传递。'},   
    yutu:{name: '玉犀灵兔',attack:0.45,defense:0.45,desc:'相传地下古城校尉的百年贴身坐骑!'},   
    xueyin:{name: '血印天龙',attack:0.4,defense:0.4,desc:'血印化身,使用精元唤醒沉睡千年之神龙!'},   
    shengdan:{name: '圣诞驯鹿',attack:0.8,defense:0.8,desc:'圣诞节特殊纪念坐骑!'},   
    kuanglei:{name: '狂雷兽',attack:0.10,defense:0.10,desc:'外表酷似麒麟，不发怒的时候，体表呈蓝紫色，犹如可爱小狗，生气时候通体闪烁电光，双目释放雷暴云般的威压。'},   
    heibai:{name: '黑白圣虎',attack:0.16,defense:0.16,desc:'远古虎族黑暗圣虎的血脉变异，实力远超黑暗圣虎，身体呈现一黑一白，真正的虎中霸者。'},   
}
const mountEquip={
    61:{title:'titieName',id:'titieId',name:'蹄铁'},
    62:{title:'maanName',id:'maanId',name:'马鞍'},
    63:{title:'jiangshengName',id:'jiangshengId',name:'缰绳'},
    64:{title:'makaiName',id:'makaiId',name:'马铠'},
    65:{title:'madengName',id:'madengId',name:'马蹬'},
    66:{title:'majueName',id:'majueId',name:'马嚼'},
}
const generalEquip={
    2:{title:'bingqiName',id:'bingqiId',name:'兵器'},
    31:{title:'maoziName',id:'maoziId',name:'帽子'},
    32:{title:'yifuName',id:'yifuId',name:'衣服'},
    33:{title:'kuziName',id:'kuziId',name:'裤子'},
    34:{title:'xieziName',id:'xieziId',name:'鞋子'},
    35:{title:'xianglianName',id:'xianglianId',name:'项链'},
    36:{title:'jiezhiName',id:'jiezhiId',name:'戒指'},
    37:{title:'shoutaoName',id:'shoutaoId',name:'手套'},
    38:{title:'jianjiaName',id:'jianjiaId',name:'肩甲'},
    39:{title:'pifengName',id:'pifengId',name:'披风'},
    40:{title:'teshuJiezhiName',id:'teshuJiezhiId',name:'特殊戒指'},
    41:{title:'zuoShoutaoName',id:'zuoShoutaoId',name:'左手套'},
    42:{title:'pkDunName',id:'pkDunId',name:'PK盾'}, 
}
//副本模板 名称/等级/要求物品/要求数量/每天可进入次数/超时时间
const dungeonConfig=[
    {name:'云树山',mapId:8930,},
    {name:'落雪山',mapId:8930,},
    {name:'进入黑风岭副本',mapId:8965},
    {name:'进入冰魄洞副本',mapId:7198,},
]
const mapConfig={
    8931:{name:'云树林',itemName:'',itemNum:0,itemName1:'',itemNum1:0,npcIdArr:[1147],msg:'从云树林边->云树林需要杀死云树林守卫。'},
    8939:{name:'小河',itemName:'',itemNum:0,itemName1:'',itemNum1:0,npcIdArr:
        [1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173],
        msg:'从云树林边->云树林需要杀死云树林守卫。'},
    8940:{name:'小路',itemName:'',itemNum:0,itemName1:'',itemNum1:0,npcIdArr:[1174],msg:'从小河->小路需要杀死大河鱼。'},
    8942:{name:'石子路',itemName:'',itemNum:0,itemName1:'',itemNum1:0,npcIdArr:[1175],msg:'从土路->石子路需要杀死大土狗。'},
    8943:{name:'云树山脚',itemName:'',itemNum:0,itemName1:'',itemNum1:0,npcIdArr:[1177],msg:'从石子路->云树山脚需要杀死大土狼。'},
    8944:{name:'山坡',itemName:'',itemNum:0,itemName1:'',itemNum1:0,npcIdArr:[1178],msg:'从云树山脚->山坡需要杀死云树山守卫。'},
    8945:{name:'云树山顶',itemName:'',itemNum:0,itemName1:'',itemNum1:0,npcIdArr:[1179],msg:'从山坡->云树山顶需要杀死云树山守将。'},
    8946:{name:'小草屋',itemName:'',itemNum:0,itemName1:'',itemNum1:0,npcIdArr:[1180],msg:'从云树山顶->小草屋需要杀死云树山二当家。'},
    8975:{name:'乱石河',itemName1:'黑狼毛',itemNum1:1,msg:'从乱石河边->乱石河需要黑狼毛x1交给黑风岭渔夫。你现在身上有'},
    6968:{name:'树洞',itemName:'青蛇皮',itemNum:1,itemName1:'',itemNum1:0,msg:'需要使用青蛇皮x1披在身上，这样才能伪装起来进入树洞而不被里面的毒蛇发现。你现在身上有'},
}
//官职
const officeConfig = {
    '平民': { val: 200, next: '小历',addDaibing:0 },
    '小历': { val: 400, next: '知事',addDaibing:0.02 },
    '知事': { val: 1600, next: '训导',addDaibing:0.04 },
    '训导': { val: 3200, next: '大使',addDaibing:0.06 },
    '大使': { val: 4800, next: '州判',addDaibing:0.08 },
    '州判': { val: 6400, next: '知县',addDaibing:0.1 },
    '知县': { val: 8000, next: '州同',addDaibing:0.12 },
    '州同': { val: 9600, next: '通判',addDaibing:0.14 },
    '通判': { val: 11200, next: '知州',addDaibing:0.16 },
    '知州': { val: 12800, next: '同知',addDaibing:0.18 },
    '同知': { val: 14400, next: '知府',addDaibing:0.2 },
    '知府': { val: 16000, next: '府丞',addDaibing:0.22 },
    '府丞': { val: 17600, next: '司运使',addDaibing:0.24 },
    '司运使': { val: 19200, next: '府尹',addDaibing:0.26 },
    '府尹': { val: 20800, next: '巡抚',addDaibing:0.28 },
    '巡抚': { val: 22400, next: '总督',addDaibing:0.3 },
    '总督': { val: 24000, next: '尚书',addDaibing:0.32 },
    '尚书': { val: 25600, next: '宰相',addDaibing:0.34 },
    '宰相': { val: 122208, next: '凡品(副)',addDaibing:0.36 }, 
    '凡品(副)': { val: 128640, next: '凡品(正)',addDaibing:0.38 },
    '凡品(正)': { val: 135072, next: '良品(副)',addDaibing:0.4 },
    '良品(副)': { val: 141504, next: '良品(正)',addDaibing:0.42 },
    '良品(正)': { val: 147936, next: '优品(副)',addDaibing:0.44 },
    '优品(副)': { val: 154368, next: '优品(正)',addDaibing:0.46 },
    '优品(正)': { val: 160800, next: '极品(副)',addDaibing:0.48 },
    '极品(副)': { val: 167232, next: '极品(正)',addDaibing:0.5 },
    '极品(正)': { val: 173664, next: '仙品(副)',addDaibing:0.52 },
    '仙品(副)': { val: 180096, next: '仙品(正)',addDaibing:0.54 },
    '仙品(正)': { val: 186528, next: '神品(副)',addDaibing:0.56 },
    '神品(副)': { val: 192960, next: '神品(正)',addDaibing:0.58 },
    '神品(正)': { val: 398784, next: '地皇',addDaibing:0.6 }, 
    '地皇': { val: 411648, next: '天尊',addDaibing:0.93 }, 
    '天尊': { val: 636768, next: '天尊(一)',addDaibing:0.96 },
    '天尊(一)': { val: 656064, next: '天尊(二)',addDaibing:0.99 },
    '天尊(二)': { val: 675360, next: '天尊(三)',addDaibing:1.02 },
    '天尊(三)': { val: null, next: null,addDaibing:1.05 } 
};
const taskListConfig=[
    {title:'主线任务',id:1,condition:{release:'map-5050'}},
    {title:'上香客的嘱托',id:2,condition:{release:'npc-530',mainTaskId:30}},
]
//武将属性处理
const generalAttrHandel=(type,itemInfo)=>{
    let str=''
    if(type=='general'){
        if(itemInfo.raguar){
            if(itemInfo.extremeRaguar){
                if(itemInfo.longpoRaguar){
                    str+=itemInfo.longpoRaguar>=100?'[龙魄狂暴]':`[龙魄狂暴:${itemInfo.longpoRaguar}/100]`;
                }else{
                    str+=itemInfo.extremeRaguar>=100?'[极度狂暴]':`[极度狂暴:${itemInfo.extremeRaguar}/100]`;
                }
            }else{
                str+=itemInfo.raguar>=100?'[狂暴]':`[狂暴:${itemInfo.raguar}/100]`;
            }
        }
        if(itemInfo.jingang){
            if(itemInfo.xueyinJingang){
                if(itemInfo.longpoJingang){
                    str+=itemInfo.longpoJingang>=100?'[龙魄金刚]':`[龙魄金刚:${itemInfo.longpoJingang}/100]`;
                }else{
                    str+=itemInfo.xueyinJingang>=100?'[血印金刚]':`[血印金刚:${itemInfo.xueyinJingang}/100]`;
                }
            }else{
                str+=itemInfo.jingang>=100?'[金刚]':`[金刚:${itemInfo.jingang}/100]`;
            }
        }


    }else if(type=='soldier'){
        if(itemInfo.raguar){
            if(itemInfo.extremeRaguar){
                if(itemInfo.longpoRaguar){
                    str+=itemInfo.longpoRaguar>=100?'[龙魄狂暴]':`[龙魄狂暴:${itemInfo.longpoRaguar}/100]`;
                }else{
                    str+=itemInfo.extremeRaguar>=100?'[极度狂暴]':`[极度狂暴:${itemInfo.extremeRaguar}/100]`;
                }
            }else{
                str+=itemInfo.raguar>=100?'[狂暴]':`[狂暴:${itemInfo.raguar}/100]`;
            }
        }
        if(itemInfo.jingang){
            if(itemInfo.xueyinJingang){
                if(itemInfo.longpoJingang){
                    str+=itemInfo.longpoJingang>=100?'[龙魄金刚]':`[龙魄金刚:${itemInfo.longpoJingang}/100]`;
                }else{
                    str+=itemInfo.xueyinJingang>=100?'[血印金刚]':`[血印金刚:${itemInfo.xueyinJingang}/100]`;
                }
            }else{
                str+=itemInfo.jingang>=100?'[金刚]':`[金刚:${itemInfo.jingang}/100]`;
            }
        }
    }else if(type=='role'){

    }
    return str;
}
//写个js函数，传入数字，每三位用逗号隔开
function formatNumberWithCommas(num) {
    if (typeof num !== 'number') {
        num = Number(num);
    }
    
    if (isNaN(num)) {
        return '0';
    }
    
    // 处理负数
    const isNegative = num < 0;
    num = Math.abs(num);
    
    // 处理整数部分
    let parts = num.toString().split('.');
    let integerPart = parts[0];
    let formattedInteger = '';
    
    for (let i = integerPart.length - 1, j = 0; i >= 0; i--, j++) {
        if (j > 0 && j % 3 === 0) {
            formattedInteger = ',' + formattedInteger;
        }
        formattedInteger = integerPart[i] + formattedInteger;
    }
    
    // 处理小数部分
    let result = isNegative ? '-' + formattedInteger : formattedInteger;
    if (parts.length > 1) {
        result += '.' + parts[1];
    }
    
    return result;
}

export {
    formatNumberWithCommas,
    taskListConfig,
    blessConfig,
    mountConfig,
    mountEquip,
    generalEquip,
    dungeonConfig,
    mapConfig,
    officeConfig,
    generalAttrHandel
}