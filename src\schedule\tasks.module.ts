import { Module } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { CommonService } from 'src/middleware/common.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleEntity } from 'src/entities/role.entity';
import { ManorEntity } from 'src/entities/manor.entity';
import { GeneralEntity } from 'src/entities/general.entity';

@Module({
    imports: [TypeOrmModule.forFeature([RoleEntity,ManorEntity,GeneralEntity])],
    controllers: [],
    providers: [TasksService,CommonService],
})
export class TasksModule {}
