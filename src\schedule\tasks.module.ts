import { Modu<PERSON> } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { CommonService } from 'src/middleware/common.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleEntity } from 'src/entities/role.entity';
import { ManorEntity } from 'src/entities/manor.entity';
import { GeneralEntity } from 'src/entities/general.entity';
import { SoldierEntity } from 'src/entities/soldier.entity';
import { NpcEntity } from 'src/entities/npc.entity';
import { RoleTasksEntity } from 'src/entities/roleTasks.entity';
import { GoodEntity } from 'src/entities/goods.entity';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';

@Module({
    imports: [TypeOrmModule.forFeature([RoleEntity,ManorEntity,GeneralEntity,SoldierEntity,GeneralEntity,NpcEntity,RoleTasksEntity,GoodEntity,PersonGoodsEntity])],
    controllers: [],
    providers: [TasksService,CommonService],
})
export class TasksModule {}
