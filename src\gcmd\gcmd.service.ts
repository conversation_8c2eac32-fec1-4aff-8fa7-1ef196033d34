import { RoleTasksEntity } from 'src/entities/roleTasks.entity';
import { HttpException, HttpStatus, Inject, Injectable, OnModuleInit, forwardRef } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { MapsEntity } from 'src/entities/maps.entity';
import { NpcEntity } from 'src/entities/npc.entity';
import { RoleEntity } from 'src/entities/role.entity';
import { TaskEntity } from 'src/entities/task.entity';
import { TasksService } from 'src/tasks/tasks.service';
import { defaultConfig, formatDuring, isPureNumber } from 'src/utils/config';
import { DataSource, Equal, In, MoreThan, MoreThanOrEqual, Repository } from 'typeorm';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { GoodsService } from './goods.service';
import { GeneralService } from './general.service';
import { FightEntity } from 'src/entities/fight.entity';
import { ManorEventEntity } from 'src/entities/manorEvent.entity';
import { PersonNpcEntity } from 'src/entities/personNpc.entity';
import * as fs from 'fs';
import { IsCaptain, paramsType, TasksType } from 'src/utils/types';
import { TeamsEntity } from 'src/entities/teams.entity';
import { CommonService } from 'src/middleware/common.service';
import { RedisService } from 'src/middleware/redis.service';
import { TeamRolesEntity } from 'src/entities/teamRoles.entity';
import { GeneralEntity } from 'src/entities/general.entity';
import { GeneralotherService } from './generalother.service';
import { dungeonConfig, mapConfig, taskListConfig } from 'src/utils/config1';
import { DungeonLogEntity } from 'src/entities/dungeonLog.entity';
import { OtherService } from './config/other.service';
import { ConfigEntity } from 'src/entities/config.entity';
import { UserConfigProvider } from 'src/common/user-config.provider';
import { OtherSeaService } from './config/otherSea.service';
import { BlessEntity } from 'src/entities/general/bless.entity';
@Injectable()
export class GcmdService implements OnModuleInit {
    private infoJson: paramsType;
    constructor(
        private readonly tasksService: TasksService,
        @Inject(forwardRef(() => CommonService))private readonly commonService: CommonService,
        @Inject(forwardRef(() => OtherService))private readonly otherService: OtherService,
        @Inject(forwardRef(() => OtherSeaService))private readonly otherSeaService: OtherSeaService,
        private eventEmitter: EventEmitter2,
        private dataSource: DataSource,
        @Inject(forwardRef(() => GoodsService)) private readonly goodService: GoodsService,
        private readonly userConfigProvider: UserConfigProvider,
        private readonly generalService: GeneralService,
        @Inject(forwardRef(() => GeneralotherService)) private readonly generalotherService: GeneralotherService,
        @InjectRepository(DungeonLogEntity) private readonly dungeonLogEntity: Repository<DungeonLogEntity>,

        @InjectRepository(TeamRolesEntity) private readonly teamRolesEntity: Repository<TeamRolesEntity>,
        private readonly redisService: RedisService,
        @InjectRepository(MapsEntity) private readonly mapsEntity: Repository<MapsEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        @InjectRepository(PersonNpcEntity) private readonly personNpcEntity: Repository<PersonNpcEntity>,
        @InjectRepository(NpcEntity) private readonly npcEntity: Repository<NpcEntity>,
        @InjectRepository(TaskEntity) private readonly taskEntity: Repository<TaskEntity>,
        @InjectRepository(ConfigEntity) private readonly configEntity: Repository<ConfigEntity>,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(FightEntity) private readonly fightEntity: Repository<FightEntity>,
        @InjectRepository(ManorEventEntity) private readonly manorEventEntity: Repository<ManorEventEntity>,
        @InjectRepository(RoleTasksEntity) private readonly roleTasksEntity: Repository<RoleTasksEntity>,
        @InjectRepository(TeamsEntity) private readonly teamsEntity: Repository<TeamsEntity>,
        @InjectRepository(GeneralEntity) private readonly generalEntity: Repository<GeneralEntity>,
        @InjectRepository(BlessEntity) private readonly blessEntity: Repository<BlessEntity>,
    ) {
        this.infoJson = JSON.parse(fs.readFileSync('./src/taskJson/info.json', 'utf8'));
    }
    onModuleInit() {

        console.log('佛挡杀佛的');
        // this.tasksService.createTimer(new Date(Date.now() + 10 * 1000), '哈哈', () => console.log('定时任务执行'))
    }
    //首页
    async home(sid: string, cmd: number, userId: string) {
        //处理第一次登录问题 移动到出生点
        const userInfo: RoleEntity = JSON.parse(await this.redisService.hget('user' + userId, 'userInfo'))
        await this.commonService.userMove(userInfo.id,9999,userInfo.gpsId,userInfo.name)
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'home', title: '游戏首页', service: 'gcmd', params: {} } }
        let content = `[内部调试阶段]<br/>刀过八荒,剑走六合!<br/>一世帝王,称霸帝王!<br/><a href="${this.seturlOther(params, urlObj, '游戏', 'main')}">进入游戏</a><br/>`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800);
        return content;
    }
    //游戏
    async main(sid: string, cmd: number, userId: string, { changeGps=null}) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'main', title: '游戏', service: 'gcmd', params: {} } }
        //获取用户信息
        const userInfo: RoleEntity = JSON.parse(await this.redisService.hget('user' + userId, 'userInfo'))
        //获取用户设置
        let userSetting=this.userConfigProvider.getUserSetting()
        //用户信息只维护任务id和地图id
        //处理玩家移动事件
        if (changeGps) {
            //处理进入树洞和乱石河 云树林
            let mapInfo=await this.getMapInfo(changeGps);
            if(mapInfo.conditionType){
                return await this.settingMenuHandle(sid,cmd,userId,{gpsId:changeGps})
            }
            this.commonService.userMove(userInfo.id,userInfo.gpsId,changeGps,userInfo.name)
            userInfo.gpsId = changeGps
        }
        let content1=await this.otherService.getTaskList(Number(userId),userInfo.gpsId)
        if(content1.type==1) {
            let content = `<p>${content1.content}<a href="${this.seturlOther(params, urlObj, '游戏', 'main')}">继续</a></p>`
            await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
            return content
        }
        
        //获取任务信息
        // const taskInfo = await this.otherService.getUserTaskList(Number(userId),userInfo.gpsId);
        //获取地图信息 npc信息
        const gpsInfo = await this.getMapInfo(userInfo.gpsId)
        let npcstr = '';
        if (gpsInfo.npcInfo.length > 0) {
            for (const item of gpsInfo.npcInfo) {
                let npcInfo=await this.getNpcInfo(item.id)
                if(npcInfo.intervalTime===0&&!gpsInfo.dungeonName){
                    if(npcInfo.directAttack){
                        npcstr += `<a class="npc" href="${this.seturlOther(params, urlObj, '战场', 'fighting', { targetID: item.id,gpsId:npcInfo.gpsId }, 'fight')}">${item.name}</a>，`
                    }else{ 
                        npcstr += await this.npcShowStatus(item.id, Number(userId)) + '<a class="npc" href="' + this.seturlOther(params, urlObj, item.name, 'npcDetail', { npcId: item.id, npcName: item.name,taskAdd:true }) + '">' + item.name + '</a>，';
                    }
                }else{
                    //有间隔时间的npc
                    let str='';
                    if(gpsInfo.dungeonName){
                        let teamRole=await this.teamRolesEntity.findOne({where:{userId:Number(userId)},relations:['team']})
                        if(teamRole){
                            str='t'+npcInfo.id+'u'+teamRole.team.id;
                        }
                    }else{
                        if(npcInfo.type==='p')str='p'+npcInfo.id+'u'+userId;
                    }
                    let ttl=await this.redisService.getstrTTL(str)
                    if(ttl!=-1){
                        if(ttl>0){
                            npcInfo.showIntervalTime==2&&(npcstr+=`${npcInfo.name}还有${formatDuring(ttl*1000)}刷新。`)
                        }else{
                            if(npcInfo.directAttack){
                                npcstr += `<a class="npc" href="${this.seturlOther(params, urlObj, '战场', 'fighting', { targetID: item.id,gpsId:npcInfo.gpsId }, 'fight')}">${item.name}</a>，`
                            }else{ 
                                npcstr += await this.npcShowStatus(item.id, Number(userId)) + '<a class="npc" href="' + this.seturlOther(params, urlObj, item.name, 'npcDetail', { npcId: item.id, npcName: item.name,taskAdd:true }) + '">' + item.name + '</a>，';
                            }
                        }
                    }
                    
                }
            }
            npcstr&&(npcstr=npcstr.slice(0,-1)+'<br/>')
        }
        //附近的玩家
        const users = JSON.parse((await this.redisService.hget('mapInfo', 'users' + userInfo.gpsId)) || '[]')
        let userstr = '';
        if (users.length > 0) {
            let teamRole=null
            if(gpsInfo.dungeonName){
                teamRole=await this.teamRolesEntity.findOne({where:{userId:Number(userId)},relations:['team']})
            }
            users.forEach(item => {
                if (item.id != userId) {
                    !userstr && (userstr += '你看到: ')
                    //如果在副本中只显示自己队伍的玩家
                    if(gpsInfo.dungeonName){
                        //副本中的玩家
                        if(teamRole.team.id==item.teamId){
                            userstr += '<a class="user" href="' + this.seturlOther(params, urlObj, item.name, 'myStatus', { targetUserId: item.id }) + '">' + item.name + '</a>，';
                        }
                    }else{
                        userstr += '<a class="user" href="' + this.seturlOther(params, urlObj, item.name, 'myStatus', { targetUserId: item.id },'otherSeaService') + '">' + item.name + '</a>，';
                    }
                }
            })
            userstr && (userstr=userstr.slice(0,-1)+'<br/>')
        }
        //附近的战场
        let fightInfo = await this.getGpsFight(userInfo.gpsId)
        let fightContentStr = ''
        fightInfo.forEach((item) => {
            if (!fightContentStr) {
                fightContentStr += '战况：</br>'
            }
            fightContentStr += `<a class="fight" href="${this.seturlOther(params, urlObj,'战场', 'fighting', { fightingId: item.id}, 'fight')}">${item.name}</a></br>`
        })
        //获取页面设置
        let setting = `
            <a class="status" href="${this.seturlOther(params, urlObj, '状态', 'myStatus')}">状态</a>
            ，<a class="goods" href="${this.seturlOther(params, urlObj, '物品', 'goodsList', {}, 'goods')}">物品</a>
            ，<a class="task" href="${this.seturlOther(params, urlObj, '任务', 'tasksList', { }, 'goods')}">任务</a>
            ，<a class="chat" href="${this.seturlOther(params, urlObj, '聊天频道', 'chatPage', { chattype: 0 }, 'goods')}">聊天</a>
            <br/>
            <a class="general" href="${this.seturlOther(params, urlObj, '武将', 'generalList', { chattype: 1 }, 'general')}">武将</a>
            ，<a class="manor" href="${this.seturlOther(params, urlObj, '庄院', 'main', {}, 'manor')}">庄院</a>
            ，<a class="fight" href="${this.seturlOther(params, urlObj, '战场列表', 'fightList', {}, 'fight')}">战场</a>
            ，<a class="team" href="${this.seturlOther(params, urlObj, '队伍', 'team', {}, 'manor')}">队伍</a>
            <br/>
            <a class="settingMenu" href="${this.seturlOther(params, urlObj, '功能菜单', 'settingMenu',{},'manor')}">功能菜单</a><br/>
        <br/>`
        let chatContent='';
        let chatType=['公聊','帮派','队伍','系统','私聊']
        let chatList=await this.redisService.lrange('chatUser'+userId,0,userSetting.pageLine)
        if(chatList&&chatList.length){
            await this.redisService.ltrim('chatUser'+userId,userSetting.pageLine,-1)
            for (const element of chatList) {
                const item1 = JSON.parse(element);
                if(item1.type==4){
                    if(!item1.count){
                        item1.count=4
                    }else{
                        item1.count-=1
                    }
                    if(item1.count>1){
                        await this.redisService.lpush('chatUser'+userId,JSON.stringify(item1))
                    }
                }
                if(item1.type==0&&userSetting.privateInfo==2){
                    continue;
                }
                if(item1.type==1&&userSetting.guildInfo==2){
                    continue;
                }
                if(item1.type==2&&userSetting.teamInfo==2){
                    continue;
                }
                let str1=`<a href="${this.seturlOther(params,urlObj,item1.userName,'myStatus',{targetUserId:item1.userId})}">${item1.userName}</a>对你说:`
                chatContent+=`[${chatType[item1.type]}] ${item1.userName?str1:''}${item1.content.replace(/<[^>]*>/g, '')}</br>`
            }
        }
        //渲染页面
        let content = `
            <p>[${gpsInfo.title}] <a href="${this.seturlOther(params, urlObj, '游戏', 'main')}">刷新</a><br/>
            ${gpsInfo.desc ? gpsInfo.desc + '</br>' : ''}
            ${npcstr}
            ${userstr}
            ${fightContentStr}
            ${chatContent}
            ${this.settingMenu(params, urlObj,gpsInfo)}
            请选择出口:<br/>
                ${gpsInfo.topId ? '<a class="top" href="' + this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: gpsInfo.topId }) + '">上:' + gpsInfo.top + '↑</a><br/>' : ''}
                ${gpsInfo.leftId ? '<a class="left" href="' + this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: gpsInfo.leftId }) + '">左:' + gpsInfo.left + '←</a><br/>' : ''}
                ${gpsInfo.rightId ? '<a class="right" href="' + this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: gpsInfo.rightId }) + '">右:' + gpsInfo.right + '→</a><br/>' : ''}
                ${gpsInfo.bottomId ? '<a class="bottom" href="' + this.seturlOther(params, urlObj, '游戏', 'main', { changeGps: gpsInfo.bottomId }) + '">下:' + gpsInfo.bottom + '↓</a><br/>' : ''}
            ${setting}
            <a href="${this.seturlOther(params, urlObj, '首页', 'home')}">游戏首页</a> 报时:${(new Date().toLocaleString()).slice(9,17)}<br/>
            <${this.infoJson.info[Math.floor(Math.random() * this.infoJson.info.length)]}>
            </p>
        `
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
        
        // console.log('用户',await this.sceneAni(sid,cmd,userId),content);
        return content;
    }
    //页面中功能选项 比如进入树洞
    settingMenu(params, urlObj,gpsInfo) {
        let str='';
        let mapName=gpsInfo.showMapName||''
        //直接传送的页面
        if(mapName){
            let arr=mapName.split('&')
            arr.forEach(item=>{
                let arr1=item.split('|')
                str+=`<a href="${this.seturlOther(params, urlObj, arr1[0], 'settingMenuHandle',{gpsId:arr1[1],hidden:true})}">${arr1[0]}</a><br/>`
            })
            return str;
        }
        //第三方页面
        if(gpsInfo.otherMap){
            let arr=gpsInfo.otherMap.split('&')
            arr.forEach(item=>{
                if(item=='爬大柱'){
                    let msg='你顺着柱子灵活地爬了上去…… '
                    let pageName='main';let serviceName='gcmd';let other={changeGps:10410}
                    str+=`<a href="${this.seturlOther(params, urlObj, '爬大柱', 'confirmPage',{msg,pageName,serviceName,other},'general')}">${item}</a><br/>`
                }
            })
        }
        return str
    }
    //页面中功能选项处理
    async settingMenuHandle(sid: string, cmd: number, userId: string, { gpsId}){
        let mapInfo=await this.getMapInfo(gpsId);
        let canEnter=true,msg=mapInfo?.msg||'你可知那江湖上，三尺青锋下埋着多少枯骨？';
        //打怪
        if(mapInfo.conditionType==1){
            msg=''
            let mapName=mapInfo?.mapName.split('|')||[]
            let npcArr=await this.goodService.getNpcIdByMapNames(mapName)
            let teamRole=await this.teamRolesEntity.findOne({where:{userId:Number(userId)},relations:['team']})
            for(let i=0;i<npcArr.length;i++){
                let npcInfoName=await this.redisService.getstr('t'+npcArr[i]+'u'+String(teamRole.team.id))
                let {keyName}=await this.redisService.hgetByPattern('npmInfoTemp','*npc'+npcArr[i]+'user'+userId)
                if(!npcInfoName||keyName){
                    msg+='怪物未清理完'
                    canEnter=false;
                    break;
                }
            }
        }
        //需要物品进入
        if(mapInfo.conditionType==2){
            let userInfo=JSON.parse(await this.redisService.hget('user'+userId,'userInfo'))
            if(mapInfo.sourceGpsId&&mapInfo.sourceGpsId==userInfo.gpsId){
                msg+='你身上有'
                let goodNameArr=mapInfo.goodName.split('&')
                for(let i=0;i<goodNameArr.length;i++){
                    let goodArr=goodNameArr[i].split('|')
                    if(goodArr&&goodArr.length==2){
                        let personGood=await this.personGoodsEntity.findOneBy({userId:Number(userId),good:{name:Equal(goodArr[0])}})
                        if(!personGood||personGood.count<goodArr[1]){
                            canEnter=false;
                            msg+=`${goodArr[0]}×${personGood?.count||0} `
                        }
                    }
                }
                if(canEnter){
                    msg=''
                    for(let i=0;i<goodNameArr.length;i++){
                        let goodArr=goodNameArr[i].split('|')
                        await this.goodService.changePersonGoodByName(Number(userId),goodArr[0],goodArr[1],'sub')
                        msg+=`失去${goodArr[0]}×${goodArr[1]} `
                    }
                }
            }else{
                msg=''
            }
            
        }
        //进入副本
        if(mapInfo.conditionType==3){
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            let dungeonLog=await this.dungeonLogEntity.find({where:{name:Equal(mapInfo.dungeonName),userId:Equal(Number(userId)),enterTime:MoreThanOrEqual(today.getTime())}});
            if(dungeonLog.find(item=>item.status===1)){
                msg=`进入${mapInfo.dungeonName}副本`
            }else{
                if(dungeonLog.length>=mapInfo.maxCount){
                    canEnter=false;
                    msg=`今天已经进入${mapInfo.dungeonName}副本${dungeonLog.length}次，请明天再来`
                }else{
                    msg+='你身上有'
                    let goodNameArr=(mapInfo.goodName?.split('&'))||[]
                    for(let i=0;i<goodNameArr.length;i++){
                        let goodArr=goodNameArr[i].split('|')
                        if(goodArr&&goodArr.length==2){
                            let personGood=await this.personGoodsEntity.findOneBy({userId:Number(userId),good:{name:Equal(goodArr[0])}})
                            if(!personGood||personGood.count<goodArr[1]){
                                canEnter=false;
                                msg+=`${goodArr[0]}×${personGood?.count||0} `
                            }
                        }
                    }
                    if(canEnter){
                        msg=`进入${mapInfo.dungeonName}副本`
                        //记录进入副本
                        let dungeonLog=new DungeonLogEntity()
                        dungeonLog.name=mapInfo.dungeonName
                        dungeonLog.userId=Number(userId)
                        dungeonLog.status=1
                        dungeonLog.enterTime=Date.now()
                        await this.dungeonLogEntity.save(dungeonLog)
                        //判断队伍是否存在不存在就创建
                        let team=await this.teamsEntity.findOne({where:{teamRoles:{userId:Number(userId)}}})
                        if(!team){
                            let userInfo=JSON.parse(await this.redisService.hget('user'+userId,'userInfo'))
                            team=new TeamsEntity()
                            team.name=userInfo.name
                            team.teamRoles=[]
                            let teamRole=new TeamRolesEntity()
                            teamRole.team=team
                            teamRole.userId=Number(userId)
                            teamRole.userName=userInfo.name
                            teamRole.isCaptain=IsCaptain.YES
                            team.teamRoles.push(teamRole)
                            await this.teamsEntity.save(team)
                        }
                        for(let i=0;i<goodNameArr.length;i++){
                            let goodArr=goodNameArr[i].split('|')
                            await this.goodService.changePersonGoodByName(Number(userId),goodArr[0],goodArr[1],'sub')
                            msg+=`失去${goodArr[0]}×${goodArr[1]} `
                        }
                    }
                }
            }
        }
        //要求主线任务完成
        if(mapInfo.conditionType==4){
            let condition=mapInfo.goodName.split('|')
            let taskInfo = await this.otherService.getUserTaskList(Number(userId));
            taskInfo=taskInfo.find(item=>item.taskName==condition[0])
            if(!taskInfo||taskInfo.stepId<condition[1]){
                canEnter=false;
            }
        }
        if(canEnter){
            //移动
            let userInfo: RoleEntity = JSON.parse(await this.redisService.hget('user' + userId, 'userInfo'))
            userInfo.gpsId = gpsId
            await this.roleEntity.update({id:Number(userId)},{gpsId:gpsId})
            await this.redisService.hset('user' + userId, 'userInfo', JSON.stringify(userInfo),1800)
        }
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        await this.redisService.hset('user' + userId, 'routerList', JSON.stringify([routerList.shift()]),1800)
        let content=await this.main(sid, cmd, userId, {});
        msg&&(content=`${msg}<br/>${content}`)
        return content;
    }
    //npc详情
    async npcDetail(sid: string, cmd: number, userId: string, { npcId, npcName,taskAdd=false}) {
        //获取npc详情
        let {keyValue:npcInfo}:any=await this.redisService.hgetByPattern('npmInfoTemp','*npc'+npcId+'user'+userId)
        if(!npcInfo){
            npcInfo=await this.getNpcInfo(npcId)
        }else{
            npcInfo=JSON.parse(npcInfo)
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'npcDetail', title: npcName, service: 'gcmd', params: { npcId, npcName } } }
        let { content, type } = await this.otherService.getTaskList(Number(userId),null,npcId)
        if (type == 1){
            let content1 = `<p>${content}<a href="${this.seturlOther(params, urlObj, npcInfo.name, 'npcDetail',{npcId,npcName})}">继续</a></p>`
            await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
            return content1;
        };
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        //购买物品
        let str = '';
        if (npcInfo.canBuy) {
            str += `<a href="${this.seturlOther(params, urlObj, '物品列表', 'npcBuy', { npcid: npcInfo.id })}">购买物品</a><br/>`
        }
        //npc设置
        if(npcInfo.config){
            npcInfo.config.split('&').forEach(item=>{
                let itemInfo=item.split('|')
                if(itemInfo[1]==1){//进入副本
                    let info=dungeonConfig.find(item1=>item1.name==itemInfo[0])
                    if(info){
                        str += `<a href="${this.seturlOther(params, urlObj, itemInfo[0], 'settingMenuHandle',{gpsId:info.mapId,hidden:true})}">${itemInfo[0]}</a><br/>`
                    }
                }else if(itemInfo[1]==2){//兑换/领取物品等
                    str += `<a href="${this.seturlOther(params, urlObj, itemInfo[0], 'npcSetting',{userId,itmeName:itemInfo[0]}),'generalother'}">${itemInfo[0]}</a><br/>`
                }else if(itemInfo[1]==3){//第三方页面
                    str += `<a href="${this.seturlOther(params, urlObj, itemInfo[0], 'thirdParty',{url:itemInfo[2]})}">${itemInfo[0]}</a><br/>`
                }else if(itemInfo[1]==4){
                    //传送 比如古墓老人的传送
                    str += `<a href="${this.seturlOther(params, urlObj, itemInfo[0], 'main',{changeGps:itemInfo[2]})}">${itemInfo[0]}</a><br/>`
                }


            })
        }
        //可攻击
        if (npcInfo.canAttack) {
            str += `<a href="${this.seturlOther(params, urlObj, '战场', 'fighting', { targetID: npcId,gpsId:npcInfo.gpsId }, 'fight')}">攻击${npcInfo.name}</a><br/>`
        }

        let defaultGeneral=npcInfo.generals[0]||{};
        let soldiersStr='',soldierNum=0,soldierCount=0;
        if(defaultGeneral.soldiers?.length){
            defaultGeneral.soldiers.forEach(item => {
                soldierNum+=item.count
                soldierCount+=item.count*item.population
                soldiersStr+=`<a href="${this.seturlOther(params,urlObj,'士兵详情','soldierDetail',{ npcId,generalId:defaultGeneral.id,soldierId:item.id})}">${item.name}</a>，`
            });
            //去掉最后一个逗号
            soldiersStr=soldiersStr.slice(0,-1)
        }
        content += `${npcInfo.name}<br/>
        ${npcInfo.desc?npcInfo.desc+'<br/>':''}
        等级: ${defaultGeneral.level}<br/>
        魄力: ${defaultGeneral.potential}<br/>
        生命: ${defaultGeneral.hpNow}/${defaultGeneral.hp}<br/>
        攻击力: ${defaultGeneral.attack}<br/>
        防御力: ${defaultGeneral.defense}<br/>
        兵器: ${defaultGeneral.wuqiStr}<br/>
        防具: ${defaultGeneral.fangjuStr}<br/>
        可带兵种: ${soldiersStr}<br/>
        带兵数量: ${soldierNum}<br/>
        带兵人口:${soldierCount}/${defaultGeneral.countAll}<br/>
        ${str}
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //npc 士兵详情
    async soldierDetail(sid: string, cmd: number, userId: string, { npcId,generalId,soldierId}) {
        const params = { sid, cmd }
        let {keyValue:npcInfo}:any=await this.redisService.hgetByPattern('npmInfoTemp','*npc'+npcId+'user'+userId)
        if(!npcInfo){
            npcInfo=await this.getNpcInfo(npcId)
        }else{
            npcInfo=JSON.parse(npcInfo)
        }
        let soldierInfo=npcInfo.generals.filter(item=>item.id==generalId)[0].soldiers.filter(item=>item.id==soldierId)[0]
        const urlObj = { [cmd]: { name: 'soldierDetail', title: soldierInfo.name, service: 'gcmd', params: { soldierId } } }
        let backRouter = await this.backRouter(userId, params, urlObj)      
        let content = `${soldierInfo.name}<br/>
            ${soldierInfo.desc?soldierInfo.desc+'<br/>':''}
            兵种等级:${soldierInfo.level}<br/>
            所属武将:<a href="${this.seturlOther(params, urlObj, 'npc详情', 'npcDetail', { npcId, npcName: npcInfo.name })}">${npcInfo.name}</a><br/>
            攻击力:${soldierInfo.attack}<br/>
            防御力:${soldierInfo.defense}<br/>
            最大生命:${soldierInfo.hp}<br/>
            兵器:${soldierInfo.wuqiStr}<br/>
            防具:${soldierInfo.fangjuStr}<br/>
            占用人口:${soldierInfo.population}<br/>
            士兵数量:${soldierInfo.count}<br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
        return content;
        
    }
    // 我的状态  accelerate加速
    /**
     * @param accelerate 加速
     * @param targetUserId 别人查看
     * @param  friend 好友 1 加 2删
     * @param black 黑名单 1 加 2删
     */
    async myStatus(sid: string, cmd: number, userId: string,{accelerate='',targetUserId=''}) {
        if(targetUserId){
            return await this.otherSeaService.myStatus(sid, cmd, userId,{targetUserId})
        }
        let userId1=Number(userId)
        //获取用户信息
        let userInfo: RoleEntity =await this.roleEntity.findOneBy({ id: userId1 })
        let userTeam=await this.teamsEntity.findOne({where:{teamRoles:{userId:userId1}}})
        let msg=''
        if(accelerate){
            let manorEventInfo=await this.manorEventEntity.findOneBy({buildId:Number(userId),userId:Number(userId),status:1})
            if(manorEventInfo){
                let time=new Date(manorEventInfo.completionTime).getTime() - new Date().getTime()
                let needPot=Math.floor(Math.floor(userInfo.level/2)*time/1000)
                if(needPot>userInfo.potential){
                    msg=`加速需要潜能：${needPot},你的潜能不够`
                }else{
                    await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
                        let maxWeight=(userInfo.level+1)*25+60
                        await transactionalEntityManager.update(RoleEntity, { id: Number(userId) }, { potential: ()=>'potential - '+needPot,level:()=>'level + '+1, upgradeStatus: 1,maxWeight });
                        await transactionalEntityManager.update(ManorEventEntity, { buildId: Number(userId), status: 1, userId: Number(userId) }, { status: 2 });
                    });
                    await this.generalService.calculatePower('role',userId1)
                    userInfo=await this.roleEntity.findOneBy({ id: Number(userId) })
                    await this.redisService.hset('user' + userId, 'userInfo', JSON.stringify(userInfo))
                }
            }
            
        }
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'myStatus', title: '状态', service: 'gcmd', params: {} } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        //判断是否升级中
        let upgradeStr='',str1='';
        let content=''
        let generalList=await this.generalEntity.find({where:{userId:userId1,near:MoreThan(0)},order:{near:'ASC'}})
        let generalStr=''
        generalList.forEach(item=>{
            generalStr+=`<a href="${this.seturlOther(params, urlObj, '武将梯队', 'armyGeneral', { generalId: item.id },'general')}">${item.name}部</a>，`
        })
        let manorEventInfo=await this.manorEventEntity.findOneBy({buildId:Number(userId),userId:Number(userId),status:1})
        if(manorEventInfo){
            let time=formatDuring(new Date(manorEventInfo.completionTime).getTime() - new Date().getTime())
            upgradeStr=`[正在升级:剩余${time}...<a href="${this.seturlOther(params, urlObj, '状态', 'myStatus',{})}">刷新</a>
            <a href="${this.seturlOther(params, urlObj, '状态', 'myStatus',{accelerate:true})}">加速</a>]`
        }else{
            upgradeStr=`[<a href="${this.seturlOther(params, urlObj, '升级', 'upgradePage', {attrItem:'role&'+userId,hidden:true},'general')}">升级</a>]`
        }
        let blessCount = await this.blessEntity.count({ where: { userId: Number(userId), status: 1 } })
        str1=`<a href="${this.seturlOther(params, urlObj, '改名', 'formInit', {obj:{title:'改名需要VIP月卡x1',type:'text',label:'新角色名称'}},'generalother')}">改名</a>，<a href="">图谱</a>，<a href="${this.seturlOther(params, urlObj, '祝福列表', 'blessingList', {type:'user',id:userId},'generalother')}">祝福(${blessCount})</a><br/>`
        content = `${msg}
        <p>${userInfo.name}<br/>
        性别:${userInfo.gender === 0 ? '男' : '女'}<br/>
        等级:${userInfo.level} ${upgradeStr}<br/>
        官职:${userInfo.office}<br/>
        潜能:${userInfo.potential}<br/>
        魄力:${userInfo.soul}<br/>
        人口:${userInfo.currentCount}/${userInfo.count}<br/>
        声望:${userInfo.prestige}<br/>
        PK数:0(已关闭)<br/>
        粮草:${userInfo.food} <a href="">转换</a><br/>
        木材:${userInfo.wood} <a href="">转换</a><br/>
        石料:${userInfo.stone} <a href="">转换</a><br/>
        生铁:${userInfo.iron} <a href="">转换</a><br/>
        银两:${userInfo.gold}<br/>
        近身军队:${generalStr}<br/>
        队伍:${userTeam?`<a href="${this.seturlOther(params, urlObj, '队伍', 'team', {teamId:userTeam.id},'manor')}">${userTeam.name}</a>`:'暂无队伍'}<br/>
        ${str1}
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //npc购买物品列表页面
    async npcBuy(sid: string, cmd: number, userId: string, { npcid }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'npcBuy', title: '物品列表', service: 'gcmd', params: { npcid } } }
        //拿到npc信息
        let npcInfo = await this.getNpcInfo(npcid)
        let str = ''
        npcInfo.goods.forEach((item, index) => {
            str += `<a href="${this.seturlOther(params, urlObj, item.name, 'npcBuyInfo', { thingId: item.id })}">${index + 1}.${item.name}(${item.price})</a><br/>`
        })
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `
            <p>${npcInfo.name}身上可购买物品有：<br/>
            ${str}
            ${backRouter}
            </p>
        `
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
        return content
    }
    //npc购买物品详情页面
    async npcBuyInfo(sid: string, cmd: number, userId: string, { thingId }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'npcBuy', title: '物品列表', service: 'gcmd', params: { thingId } } }
        let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: Number(userId) });
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let goodInfo = await this.goodService.getGoodDetail(thingId)
        let content = `
            <p>${goodInfo.name}<br/>
            类别:兵器.刀<br/>
            重量:${goodInfo.weight}<br/>
            价格:${goodInfo.price}<br/>
            ${goodInfo.addatk ? '攻击力:' + goodInfo.addatk + '<br/>' : ''}
            ${goodInfo.adddef ? '防御力:' + goodInfo.adddef + '<br/>' : ''}
            ${goodInfo.addhp ? '抵消伤害:' + goodInfo.addhp + '<br/>' : ''}
            使用条件:${goodInfo.useLevel}级以上<br/>
            你身上有${userInfo.gold}银两<br/>
            你当前的负重是:${userInfo.nowWeight}/${userInfo.maxWeight}
            </p>
            <form action="${this.seturlOther(params, urlObj, '购买物品', 'buyInfoFn', { thingId })}" method="post">
            请输入你要购买的数量：<input name="count" type="number" value="1" min="1"/><br/>
            <input name="submit" type="submit" title="购买" value="购买"/></form>
            ${backRouter}
        `
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redisService.hset('user' + userId, 'token', userId,1800)
        return content
    }
    //npc购买物品 成功返回上一页 否则返回错误提示
    async buyPost(userId: number, thingId: number, count: number, cmd: number, sid: string) {
        count = Number(count);
        let msg = '', content = '';
        let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: userId });
        let goodInfo = await this.goodService.getGoodDetail(thingId)
        if (!isPureNumber(count) || count < 1 || count > 9999) {
            msg = '数量不正确'
        } else {
            if (userInfo.maxWeight - userInfo.nowWeight < count) msg = '你的负载过重,不能装过多的物品'
            if (goodInfo.price * count > userInfo.gold) msg = '你没有足够的银两购买' + goodInfo.name + '*' + count
        }

        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        if (msg) {
            content = '<p style="color:red">' + msg + '</p>'
            let routerInfo = routerList.pop()
            content += await this[routerInfo.name](sid, cmd, userId, routerInfo.params)
            return content
        }
        let good = await this.goodService.getGoodNum(userId, thingId)
        await this.dataSource.transaction(async (manager) => {
            if (good.id) {//存在就更新
                await manager.update(PersonGoodsEntity, { id: good.id }, { count: () => "count + " + count })
            } else {
                await manager.insert(PersonGoodsEntity, {
                    count,
                    userId: userInfo.id,
                    attack: goodInfo.addatk,
                    defense: goodInfo.adddef,
                    hp: goodInfo.addhp,
                    durability: goodInfo.durability||99999,
                    good: { id: thingId }
                })
            }

            //扣除用户银两
            userInfo.gold -= goodInfo.price * count
            userInfo.nowWeight += count
            userInfo = await manager.save(RoleEntity, userInfo)
            await this.redisService.hdel('user' + userId, 'token')
        }) 
        await this.goodService.updateUserWeight(Number(userId))
        //写入日志
        this.eventEmitter.emit('writeLogs', { userId, name: '购买物品:' + goodInfo.name, gold: goodInfo.price * count })
        let routerInfo = await this.commonService.getPrevRouter3(userId)
        content = `你用${goodInfo.price * count}银两购买了${goodInfo.name}×${count}</br>`
        content += await this[routerInfo.name](sid, cmd, userId, routerInfo.params)
        return content
    }
    //模板
    async moban(sid: string, cmd: number, userId: string) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'home', title: '游戏首页', service: 'gcmd', params: {} } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = ``
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //根据地图id获取地图信息 缓存没有就查数据库  
    async getMapInfo(mapId: number) {
        let mapInfo = JSON.parse((await this.redisService.hget('mapInfo', String(mapId))) ?? '{}')
        if (Object.keys(mapInfo).length === 0) {
            console.log('缓存没有');
            mapInfo = await this.mapsEntity.findOne({ where: { id: mapId } })
            if (!mapInfo) throw new HttpException('地图不存在', HttpStatus.FORBIDDEN);
            const arr = [mapInfo.topId, mapInfo.bottomId, mapInfo.leftId, mapInfo.rightId]
            const result = await this.mapsEntity.find({ where: { id: In(arr) }, select: ['id', 'title'] })
            result.forEach(item => {
                if (item.id == mapInfo.topId) {
                    mapInfo.top = item.title
                } else if (item.id == mapInfo.bottomId) {
                    mapInfo.bottom = item.title
                } else if (item.id == mapInfo.leftId) {
                    mapInfo.left = item.title
                } else if (item.id == mapInfo.rightId) {
                    mapInfo.right = item.title
                }
            })
            //npc信息
            const npcInfo = await this.npcEntity.find({ where: { gpsId: mapId }, select: ['id', 'name','type'] })
            mapInfo.npcInfo = npcInfo
            await this.redisService.hset('mapInfo', String(mapId), JSON.stringify(mapInfo))
        }
        return mapInfo

    }
    //获取npc详情
    async getNpcInfo(npcId: number) {
        let npcInfo = await this.redisService.hget('npcInfo', String(npcId))
        if(npcInfo==null||npcInfo=='null'){
            let npcInfo = await this.npcEntity.findOne({ where: { id: npcId }, relations: ['goods','npcGoods', 'generals', 'generals.soldiers'] })
            await this.redisService.hset('npcInfo', String(npcId), JSON.stringify(npcInfo))
            return npcInfo
        }else{
            return JSON.parse(npcInfo)
        }
    }
    //获取任务详情
    async getTaskInfo(taskId: number) {
        let taskInfo=await this.redisService.hget('taskInfo', String(taskId))
        if(taskInfo==null||taskInfo=='null'){
            let taskInfo = await this.taskEntity.findOne({ where: { id: taskId } })
            await this.redisService.hset('taskInfo', String(taskId), JSON.stringify(taskInfo))
            return taskInfo
        }else{
            return JSON.parse(taskInfo)
        }
    }
    //设置返回路由
    async backRouter(userId, params, urlObj) {
        let str = ''
        //返回路由
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        if(routerList.length>1){
            routerList.reverse().shift()
        }
        routerList.forEach(element => {
            if(!element.params.hidden){
                str += `<a href="${this.seturlOther(params, urlObj, element.title, element.name, element.params, element.service)}">返回${element.title}</a><br/>`
            }
        });
        return str
    }
    /**
     * 
     * @param params cnd sid参数 
     * @param urlObj url集合
     * @param title 页面标题
     * @param name 页面名称
     * @param param 请求参数
     * @param service 页面服务名
     * @returns string 页面url
}
     */
    seturlOther(params, urlObj, title: string, name: string, attr = {}, service = 'gcmd') {
        params.cmd++
        urlObj[params.cmd] = { title, name, service, params: attr }
        return `/gcmd?sid=${params.sid}&cmd=${params.cmd}`
    }
    //npc前边是否显示问号或叉号
    /**
     * 
     * @param taskInfo 任务详情
     * @param npcId 当前npcid
     * @returns 
     */
    async npcShowStatus(npcId: number, userId: number) {
        const userTaskList=await this.otherService.getUserTaskList(userId)
        for(let i=0;i<taskListConfig.length;i++){
            let taskInfo=taskListConfig[i]
            let userTask=userTaskList.find(item=>item.taskId==taskInfo.id)
            if(userTask){
                let msg=''
                let taskJson=await this.otherService.getTaskListByTaskId(userTask.taskId,userTask.stepId)
                let step=userTask.stepId-taskJson.initIndex
                let prevTask=JSON.parse(JSON.stringify(taskJson.steps[step]));
                if(taskJson.steps[step].condition.release=='npc-'+npcId){
                    msg=`<img src="${defaultConfig.chahao}"/>`
                }
                let nestTask=null
                if(taskJson.steps.length==step+1){
                    if(taskInfo.id==1){
                        let nextStep=userTask.stepId
                        nextStep=Math.ceil(nextStep/1000) * 1000
                        taskJson=this.otherService.getTaskListByTaskId(taskInfo.id,nextStep)
                        nestTask=taskJson.steps[0]
                    }else{
                        msg=''
                    }
                }else{
                    nestTask=taskJson.steps[step+1]
                }
                if(nestTask){
                    if(nestTask.condition.release=='npc-'+npcId&&(await this.checkTaskStatus(prevTask.condition, userId)).bool){
                        msg=`<img src="${defaultConfig.duihao}"/>`
                    }
                }
                return msg
            }else{
                if(taskInfo.condition.release=='npc-'+npcId){
                    return `<img src="${defaultConfig.duihao}"/>`
                }
            }
        }
    }
    //获取用户任务列表
    async getUserTaskList(userId: number) {
        let taskList=await this.redisService.hget('user'+userId, 'taskList')
        if(taskList==null||taskList=='null'){
            let taskList = await this.roleTasksEntity.find({ where: {role:{id: userId}} })
            await this.redisService.hset('user'+userId, 'taskList', JSON.stringify(taskList))
            return taskList
        }else{
            return JSON.parse(taskList)
        }
    }
    // 获取所有战场，先尝试从缓存读取，如果缓存不存在则从数据库获取
    async getGpsFight(gpsId) {
        let gpsfightInfo: FightEntity[] = JSON.parse(await this.redisService.hget('gpsfight', String(gpsId)))
        if (!gpsfightInfo) {
            gpsfightInfo = await this.fightEntity.findBy({ gpsId, status: 1 });
            await this.redisService.hset('gpsfight',String(gpsId), JSON.stringify(gpsfightInfo));
        }
        return gpsfightInfo;
    }
    //判断任务完成状态
    /**
     * 
     * @param taskInfo 任务详情
     * @param userId 用户id
     * @returns 
     */
    async checkTaskStatus(taskInfo, userId: number) {
        if (!taskInfo?.condition)return {bool:true,msg:''}
        //打造庄园
        if (taskInfo.condition == 1) {
            let manorInfo = (await this.commonService.getManorInfo(userId + '', 1))[0]
            let lvl=manorInfo?.level||0
            let obj={
                bool:lvl >= 1,
                msg:'庄园等级'+lvl+'/1</br>'
            }
            return obj
        }
        //物品任务 怪物
        if (taskInfo.condition == 2) {
            let bool=true,msg=''
            let goodNames=taskInfo.goods?.map(item=>item.name)
            if(goodNames?.length){
                let goods=await this.goodService.getGoodNumByNameArr(userId, goodNames)
                goods.forEach(item=>{
                    let good=taskInfo.goods.find(item1=>item1.name==item.name)
                    msg+=`${item.name} ${item.count}/${good.num} `
                    if(item.count<good.num){
                        bool=false
                    }
                })
            }
            //怪物
            let monsterNames=taskInfo.monsters?.map(item=>item.name)
            if(monsterNames?.length){
                let monsters=await this.personNpcEntity.find({where:{userId,npcName:In(monsterNames)}})
                for (const element of taskInfo.monsters) {
                    let monster=monsters.find(item=>item.npcName==element.name)
                    let num=0
                    if(!monster){
                        bool=false
                    }else{
                        num=monster.count
                        if(monster.count<element.num){
                            bool=false
                        }
                    }
                    msg+=`${element.name} ${num}/${element.num} `
                }
            }
            return {
                bool,
                msg
            }
        }
        //人物、武将、兵种升级到指定等级
        if (taskInfo.condition == 3) {
            let bool=true,msg=''
            if(taskInfo.generals?.length){
                for (const element of taskInfo.generals) {
                    let generalInfo=await this.generalService.getGeneralDetailByName(userId,element.name)
                    if(!generalInfo){
                        bool=false
                        return {bool,msg:'没有武将'}
                    }
                    if(element.type){
                        //士兵
                        let soldier=generalInfo.soldiers.find(item=>item.name==element.type)
                        if(!soldier){
                            bool=false
                            return {bool,msg:'没有士兵'}
                        }
                        bool = soldier.level >= element.level;
                        msg += `${element.name}/${element.type}等级 ${soldier.level}/${element.level} `;
                    }else{
                        //比较武将
                        bool = generalInfo.level >= element.level;
                        msg += `${element.name}等级 ${generalInfo.level}/${element.level} `;
                    }
                }
            }
            if(taskInfo.role){

            }
            return {
                bool,
                msg
            }
        }
        //任务条件4 为武将招募士兵
        if (taskInfo.condition == 4) {
            let bool=true,msg=''
            if(taskInfo.generals?.length){
                for (const element of taskInfo.generals) {
                    let generalInfo=await this.generalService.getGeneralDetailByName(userId,element.name)
                    let soldier=generalInfo.soldiers.find(item=>item.name==element.type)
                    bool = soldier.count >= element.num;
                    msg += `${element.name}/${element.type}数量 ${soldier.count}/${element.num} `;
                }
            }
            return {
                bool,
                msg
            }
        }
        //需要资源
        if (taskInfo.condition == 5) {
            let bool=true,msg=''
            let userInfo=await this.roleEntity.findOne({where:{id:userId}})
            if(taskInfo.resource.food&&userInfo.food<taskInfo.resource.food){
                bool=false
                msg='粮草不足'+userInfo.food+'/'+taskInfo.resource.food
            }
            if(taskInfo.resource.wood&&userInfo.wood<taskInfo.resource.wood){
                bool=false
                msg='木材不足'+userInfo.wood+'/'+taskInfo.resource.wood
            }
            if(taskInfo.resource.stone&&userInfo.stone<taskInfo.resource.stone){
                bool=false
                msg='石料不足'+userInfo.stone+'/'+taskInfo.resource.stone
            }
            if(taskInfo.resource.iron&&userInfo.iron<taskInfo.resource.iron){
                bool=false
                msg='生铁不足'+userInfo.iron+'/'+taskInfo.resource.iron
            }
            if(taskInfo.resource.gold&&userInfo.gold<taskInfo.resource.gold){
                bool=false
                msg='银两不足'+userInfo.gold+'/'+taskInfo.resource.gold    
            }
            return {bool,msg}
        }
        //任务条件6 为武将装备兵器
        if (taskInfo.condition == 6) {
            let bool=true,msg=''
            if(taskInfo.generals?.length){
                for (const element of taskInfo.generals) {
                    let generalInfo=await this.generalService.getGeneralDetailByName(userId,element.name)
                    if(!generalInfo){
                        msg+=`${element.name}没有武将 `
                        bool=false
                        continue
                    }
                    if(element.type){
                        let soldier=generalInfo.soldiers.find(item=>item.name==element.type)
                        if(!soldier||soldier.bingqiName != element.weapon){
                            msg+=`${element.name}的${soldier.name}没有装备${element.weapon} `
                            bool=false
                        }
                    }else{
                        if(generalInfo.bingqiName != element.weapon){
                            msg+=`${element.name}没有装备${element.weapon} `
                            bool=false
                        }
                    }
                    
                }
            }
            return {bool,msg}
        }
        return {bool:false,msg:''}
    }
    //去掉任务物品
    async removeTaskGood(userId:number,taskInfo){
        //多个任务物品和怪物的情况
        if(taskInfo.goods?.length){
            for (const element of taskInfo.goods) {
                await this.goodService.changePersonGoodByName(userId,element.name,element.num,'sub')
            }
            await this.goodService.updateUserWeight(userId)
        }
        if(taskInfo.monsters?.length){
            let monsterName=taskInfo.monsters.map(item=>item.name)
            await this.personNpcEntity.delete({userId:userId,npcName:In(monsterName)})
        }
    }
     //中间页 提示
    async getMiddlePage(sid: string, cmd: number,userId:string,{msg}){
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'getMiddlePage', title: '提示',service:'general', params:{hidden:true} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = msg+'<br/>'+backRouter
        await this.redisService.hset('user'+userId,'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //发放奖励，更新用户信息 写入日志
    /**
     * 
     * @param userId 用户id
     * @param taskId 任务id
     */
    async giveAward(userId: number,taskId,taskInfo) {
        if(!taskInfo)return
        //奖励物品
        if(taskInfo.rewardId){
            let res=await this.goodService.changePersonGood(userId,taskInfo.rewardId,taskInfo.rewardNum,'add')
            if(res){  
                this.eventEmitter.emit('writeLogs', { userId, name: `任务奖励物品,任务id${taskId}物品id${taskInfo.rewardId}/物品数量${taskInfo.rewardNum}` })
            }
        }
        if(taskInfo.reward){
            let rewardArr=taskInfo.reward.split('&')
            rewardArr.forEach(item=>{
                let [name,num]=item.split('|')
                this.goodService.changePersonGoodByName(userId,name,num,'add')
                this.eventEmitter.emit('writeLogs', { userId, name: `任务奖励物品,任务id${taskId}物品名称${name}/物品数量${num}` })
            })
        }
        //没有奖励
        if (!(taskInfo.food || taskInfo.potential || taskInfo.wood || taskInfo.stone || taskInfo.iron || taskInfo.gold)) return
        // ... existing code ...

        let updateFields = {};

        if (taskInfo.food) updateFields['food'] = () => `food + ${taskInfo.food}`;
        if (taskInfo.potential) updateFields['potential'] = () => `potential + ${taskInfo.potential}`;
        if (taskInfo.wood) updateFields['wood'] = () => `wood + ${taskInfo.wood}`;
        if (taskInfo.stone) updateFields['stone'] = () => `stone + ${taskInfo.stone}`;
        if (taskInfo.iron) updateFields['iron'] = () => `iron + ${taskInfo.iron}`;
        if (taskInfo.gold) updateFields['gold'] = () => `gold + ${taskInfo.gold}`;

        if (Object.keys(updateFields).length > 0) {
            let res = await this.roleEntity.update({ id: userId }, updateFields);
            if (res.affected > 0) {
                this.eventEmitter.emit('writeLogs', { 
                    userId, 
                    name: `任务奖励:任务id${taskId}`, 
                    food: taskInfo.food, 
                    potential: taskInfo.potential, 
                    wood: taskInfo.wood, 
                    stone: taskInfo.stone, 
                    iron: taskInfo.iron, 
                    gold: taskInfo.gold 
                });
            }
        }

// ... existing code ...
    }

}
