import { RouterType, RouterListType } from "./types";
// <style>.npc{color:#FF4F0F}
//     .user{color:#FFA673}
//     .fight{color:#008000}
//     .status{color:#7F55B1}
//     .goods{color:#9B7EBD}
//     .task{color:#F49BAB}
//     .chat{color:#FFE1E0}
//     .general{color:#8F87F1}
//     .manor{color:#C68EFD}
//     .fight{color:#E9A5F1}
//     .team{color:#FED2E2}
//     .settingMenu{color:#5F8B4C}
//     .left{color:#C68EFD}
//     .right{color:#C68EFD}
//     .top{color:#C68EFD}
//     .bottom{color:#C68EFD}
//     </style>
const defaultConfig = {
    htmlHeader: `<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>永恒帝王</title>
    <style>.npc{color:#FF4F0F}
    </style>
    </head><body style="background-color:#FFFAE8; font-family: Arial, sans-serif;padding: 20px; margin: 0;">`,
    htmlfooter: `</body></html>`,
    sendRequest: `
        <script>
            function sendRequest(url){
                const formData = new FormData(document.querySelector('form'));
                let jsonObject = {};
                formData.forEach((value, key) => {
                    jsonObject[key] = Number(value) || value;
                });
                fetch(url, {
                    method: 'POST',
                    headers: {
                        "Content-type": "application/json; charset=utf-8",
                    },
                    body: JSON.stringify(jsonObject) 
                }).then(response => response.json())
                .then(res => {
                    if(res.msg){
                        let errorDiv = document.getElementById('error');
                        errorDiv.innerHTML = res.msg;
                        errorDiv.style.display ='block';
                    }else{
                        location.href = res.url;
                    }

                }).catch(error => {
                    console.error('Error:', error); 
                });
            }
        </script>
    `,
    duihao: 'data:image/gif;base64,R0lGODlhCAAQANUtAOTk8QCIRACBP2a1Xqus01urdtTq1gdvVRCJSzGfWCWTUQF5QySaTfD39JewvvLy+OLy6aDPt1lon0NOkgB/P9Dn27fct8Dh0MDg0PX69VCsfnJ0tXi+cUFjd6DQuHC2kydnaJ2ey1ybZ0uAjWS5jHC+j0CebvH59GG1czWGcWahlziiUgSMRv///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAC0ALAAAAAAIABAAAAYywJZwSCwahaYjxahZGAOppzFxKFoYoyJHMUS0DAOQ8CNgoQaSYXklKrJWHeNqYixshkEAOw==',
    chahao: 'data:image/gif;base64,R0lGODlhCAAQAOZBANxkDM7FzuKRXNVeEupwA/fh09lhDuFoCeeaY2U9SvnNpls4TeaXY9hgD+J4KvW3gfChTY98j/S+iPOybvLNtu+AHPvt4dhoIOCGTf318O6cRNZfEMBYEuqDIH84JPXXxPW2ep5bOddfEPTQtpmBi+dtBeeMR8piDtlhD/jQp+13CeeIPumYSNxzLux0BbtfIuBmCqJ1aeCHTOecZO2xifOhVuRqB6iaqPPx8++TQqlUHO99FNVeEcmjj/748tpiDvSnYv///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAEEALAAAAAAIABAAAAdPgEGCg4QCAwMCgxciKAYNPBRBPx4LCRwALQYHIYM2AA4lKoMEB0EPhC4vhEAVJySEOzoRhEEEtEEmMLcyG7cjtykSGQy0HSszhBMaLAiCgQA7',
}
function filterRouter(routerList: RouterListType, router: RouterType) {
    if (router.name == 'home') return routerList
    for (let index = 0; index < routerList.length; index++) {
        const element = routerList[index];
        if (element.name == router.name && element.service == router.service) {
            element.params=router.params//把最后路由的参数赋值给当前路由
            return routerList.slice(0, index + 1)
        }
    }
    routerList.push({ name: router.name, service: router.service, params: router.params, title: router.title })

    return routerList
}
const equipmentType = [
    { id: 30, name: '兵器' },
    { id: 31, name: '帽子' },
    { id: 32, name: '衣服' },
    { id: 33, name: '裤子' },
    { id: 34, name: '鞋子' },
    { id: 35, name: '项链' },
    { id: 36, name: '戒指' },
    { id: 37, name: '手套' },
    { id: 38, name: '肩甲' },
    { id: 39, name: '披风' },
    { id: 40, name: '特殊戒指' },
    { id: 41, name: '左手套' },
    { id: 42, name: 'PK盾' },
]
const equipmentMountType = [
    { id: 51, name: '蹄铁' },
    { id: 52, name: '马鞍' },
    { id: 53, name: '缰绳' },
    { id: 54, name: '马铠' },
    { id: 55, name: '马蹬' },
    { id: 56, name: '马嚼' },
]
const goodsType = [
    //1:消耗品 2:兵器 3:防具 4:士兵兵器 5:士兵防具 6:坐骑装备 7:装备宝石 8:其它 
    { id: 1, name: '消耗品' },
    {
        id: 2, name: '兵器',
        child: [
            //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪
            { id: 21, name: '刀' },
            { id: 22, name: '剑' },
            { id: 23, name: '棍' },
            { id: 24, name: '斧' },
            { id: 25, name: '锤' },
            { id: 26, name: '枪' },
        ]
    },
    {
        id: 3, name: '防具',
        child: [
            //31:帽子  32:衣服  33:裤子  34:鞋子  35:项链  36:戒指  37:手套  38:肩甲  39:披风  40:特殊戒指 41:左手套 42:PK盾
            { id: 31, name: '帽子' },
            { id: 32, name: '衣服' },
            { id: 33, name: '裤子' },
            { id: 34, name: '鞋子' },
            { id: 35, name: '项链' },
            { id: 36, name: '戒指' },
            { id: 37, name: '手套' },
            { id: 38, name: '肩甲' },
            { id: 39, name: '披风' },
            { id: 40, name: '特殊戒指' },
            { id: 41, name: '左手套' },
            { id: 42, name: 'PK盾' },
        ]
    },
    {
        id: 4, name: '士兵兵器',
        child: [
            //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪
            { id: 21, name: '刀' },
            { id: 22, name: '剑' },
            { id: 23, name: '棍' },
            { id: 24, name: '斧' },
            { id: 25, name: '锤' },
            { id: 26, name: '枪' },
        ], typeId: 'bingqiId', typeName: 'bingqiName'
    },
    {
        id: 5, name: '士兵防具', child: [
            //31:帽子  32:衣服  33:裤子  34:鞋子  35:项链  36:戒指  37:手套  38:肩甲  39:披风  40:特殊戒指 41:左手套 42:PK盾
            { id: 31, name: '帽子', typeId: 'maoziId', typeName: 'maoziName' },
            { id: 32, name: '衣服', typeId: 'yifuId', typeName: 'yifuName' },
            { id: 33, name: '裤子', typeId: 'kuziId', typeName: 'kuziName' },
            { id: 34, name: '鞋子', typeId: 'xieziId', typeName: 'xieziName' },
            { id: 35, name: '项链', typeId: 'xianlianId', typeName: 'xianlianName' },
            { id: 36, name: '戒指', typeId: 'jieziId', typeName: 'jieziName' },
            { id: 37, name: '手套', typeId: 'shougaoId', typeName: 'shougaoName' },
            { id: 38, name: '肩甲', typeId: 'jianjiaId', typeName: 'jianjiaName' },
            { id: 39, name: '披风', typeId: 'pifengId', typeName: 'pifengName' },
            { id: 40, name: '特殊戒指', typeId: 'jieshuId', typeName: 'jieshuName' },
            { id: 41, name: '左手套', typeId: 'zuoshoutaoId', typeName: 'zuoshoutaoName' },
            { id: 42, name: 'PK盾', typeId: 'pkdunId', typeName: 'pkdunName' },
        ]
    },
    {
        id: 6, name: '坐骑装备', child: [
            //51:蹄铁 52:马鞍 53:缰绳 54:马铠 55:马蹬 56:马嚼
            { id: 51, name: '蹄铁' },
            { id: 52, name: '马鞍' },
            { id: 53, name: '缰绳' },
            { id: 54, name: '马铠' },
            { id: 55, name: '马蹬' },
            { id: 56, name: '马嚼' },
        ]
    },
    { id: 7, name: '装备宝石' },
    {
        id: 8, name: '其它', child: [
            //71:商城 72:强化 73:材料 74:训练 75:任务 76:活动 77:神兵
            { id: 71, name: '商城' },
            { id: 72, name: '强化' },
            { id: 73, name: '材料' },
            { id: 74, name: '训练' },
            { id: 75, name: '任务' },
            { id: 76, name: '活动' },
            { id: 77, name: '神兵' },
            { id: 78, name: '其它' },
        ]
    },
]
const goodsType1 = [
    //1:消耗品 2:兵器 3:防具 4:士兵兵器 5:士兵防具 6:坐骑装备 7:装备宝石 8:其它 
    { id: 1, name: '消耗品' },
    {
        id: 8, name: '其它', child: [
            //71:商城 72:强化 73:材料 74:训练 75:任务 76:活动 77:神兵
            { id: 71, name: '商城' },
            { id: 72, name: '强化' },
            { id: 73, name: '材料' },
            { id: 74, name: '训练' },
            { id: 75, name: '任务' },
            { id: 76, name: '活动' },
            { id: 77, name: '神兵' },
            { id: 78, name: '其它' },
        ]
    },
]
//随机整数
function getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
//随机数
/**
 * 
 * @param min 最小值
 * @param max 最大值
 * @returns 
 */
function getRandomNumber(min, max) {
    return Math.random() * (max - min) + min;
}
// 建筑配置
const buildconfig = {
    //粮草 木材 石料 生铁 银两 时间
    //石料 生铁减半 2 2 1 1 2 2
    'zhuangyuan': {
        val: [100, 300, 1000, 5000, 30000, 100000, 400000],
        time: [100, 300, 1000, 5000, 30000, 80000, 200000]
    },
    'fangwu': {
        time: [65, 75, 110, 310, 1560, 4060],
    },
    // 3 1
    'nongtian': {
        time: [90, 150, 360, 1560, 9060, 24060],
    },
    //类型对应的type值
    type: {
        'zhuangyuan': 1,
        'fangwu': 2,
        'nongtian': 3,
        'famu': 4,
        'caishi': 5,
        'tiekuang': 6,
        'tiejiang': 7,
        'caifeng': 8,
        'shuyuan': 9,
        'bingbu': 10,
        'mapeng': 11,
    }
}
const buildType = {
    zhuangyuan: {
        desc: '此建筑是建造其它任何建筑的前提。等级越高，可建造的建筑数量、同时可以进行的事件就越多。',
        name: '庄院',
        type: 1
    },
    fangwu: {
        desc: '房屋提升人口。',
        name: '房屋',
        type: 2
    },
    nongtian: {
        desc: '可以产出粮食。',
        name: '农田',
        type: 3
    },
    famu: {
        desc: '可以产出木材。',
        name: '伐木场',
        type: 4
    },
    caishi: {
        desc: '可以产出石料。',
        name: '采石场',
        type: 5
    },
    tiekuang: {
        desc: '可以产出生铁。',
        name: '铁矿场',
        type: 6
    },
    tiejiang: {
        desc: '铁匠铺用于打造各种兵器。',
        name: '铁匠铺',
        type: 7
    },
    caifeng: {
        desc: '裁缝铺用于制作各种防具。',
        name: '裁缝铺',
        type: 8
    },
    shuyuan: {
        desc: '研究生产工艺的地方。其研究的技能可以使自己资源场增产、加快各种建造需要的时间等。',
        name: '书院',
        type: 9
    },
    bingbu: {
        desc: '研究军事技能的地方。其研究的技能可以使自己的武将、士兵战斗能力加强。',
        name: '兵部',
        type: 10
    },
    mapeng: {
        desc: '饲养坐骑的地方。',
        name: '马棚',
        type: 11
    }
}
function handlbuildfn(type, lvl) {
    const obj = buildType
    if (type === 'zhuangyuan') {
        let val = buildconfig.zhuangyuan.val[lvl - 1]
        return {
            food: val,
            wood: val,
            stone: val / 2,
            iron: val / 2,
            gold: val,
            desc: obj[type].desc,
            name: obj[type].name,
            time: val
        }
    }
    if (type === 'fangwu') {
        let val = (buildconfig.zhuangyuan.val[lvl - 1]) / 10
        return {
            food: val,
            wood: val,
            stone: val / 2,
            iron: val / 2,
            gold: val,
            desc: obj[type].desc,
            name: obj[type].name,
            time: buildconfig.fangwu.time[lvl - 1]
        }
    }
    if (['nongtian', 'famu', 'caishi', 'tiekuang'].includes(type)) {
        let val = (buildconfig.zhuangyuan.val[lvl - 1]) / 10
        return {
            food: type === 'nongtian' ? val * 3 : val,
            wood: type === 'famu' ? val * 3 : val,
            stone: type === 'caishi' ? val * 3 : val,
            iron: type === 'tiekuang' ? val * 3 : val,
            gold: val,
            desc: obj[type].desc,
            name: obj[type].name,
            time: buildconfig.nongtian.time[lvl - 1]
        }
    }
    let val = (buildconfig.zhuangyuan.val[lvl - 1]) / 2
    return {
        food: val,
        wood: val,
        stone: val / 2,
        iron: val / 2,
        gold: val,
        desc: obj[type].desc,
        name: obj[type].name,
        time: val
    }
}
//建筑等级对应的建筑数、事件数
function manorEventfn(lvl) {
    const build = () => {
        return 9 +lvl* 4
    }
    const event = () => {
        return 3 + lvl* 2
    }
    return {
        buildNUm: build(),
        eventNum: event(),
    }
}
//毫秒换算成天时分秒
function formatDuring(mss) {
    var days = Math.floor(mss / (3600 * 24 * 1000));
    var hours = Math.floor((mss % (3600 * 24 * 1000)) / (3600 * 1000));
    var minutes = Math.floor((mss % (3600 * 1000)) / (60 * 1000));
    var seconds = Math.floor((mss % (60 * 1000)) / 1000);
    var result = "";
    if (days > 0) {
        result += days + "天";
    }
    if (hours > 0) {
        result += hours + "时";
    }
    if (minutes > 0) {
        result += minutes + "分";
    }
    if (seconds > 0 || result === "") {
        result += (seconds < 0 ? 0 : seconds) + "秒";
    }
    return result
}
//处理时间 [Fri Jul 05 2024 16:36:33 GMT+0800 (中国标准时间)] => 07-05 16:36:33
function formatDate(timeString, type = 1) {
    const date = new Date(timeString);
    const month = date.getMonth() + 1; // 月份是从0开始的，所以需要加1
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    function addZero(num) {
        return num < 10 ? "0" + num : num;
    }
    let str = ''
    type == 1 && (str = addZero(month) + "-" + addZero(day) + " " + addZero(hours) + ":" + addZero(minutes))
    type == 2 && (str = addZero(hours) + ":" + addZero(minutes))
    return str
}
//字符串截取
function extractBetween(str, start, end) {
    if (!str) return ''
    let adjustedStartIndex = 0, adjustedEndIndex = str.length;
    if (start) {
        // 查找开始字符的位置
        const startIndex = str.indexOf(start);
        // 如果开始字符不存在，则从头开始
        adjustedStartIndex = startIndex + start.length;
    }

    if (end) {
        // 从调整后的开始位置查找结束字符的位置
        const endIndex = str.indexOf(end, adjustedStartIndex);
        // 如果结束字符不存在，则截取到字符串末尾
        adjustedEndIndex = endIndex;
    }


    // 返回开始字符（或字符串开头）和结束字符（或字符串末尾）之间的字符串
    return str.substring(adjustedStartIndex, adjustedEndIndex);
}
// 技能模板   名称/伤害系数/攻击距离/攻击数量/武器类型/恢复时间
const skillTemplate = {
    "puTongGongJi": ["普通攻击", 2, 1, 1, 0],
    "daDaoKanShu": ["大刀砍术", 66, 2, 2, 21],
    "yanLingDaoFa": ["雁翎刀法", 66, 3, 2, 21],
    "luanGun": ["乱棍", 40, 2, 2, 23],
    "wuShengDaoFa": ["无声刀法", 30, 3, 2, 21],
    "yuXinJianFa": ["玉心剑法", 71, 2, 3, 22],
    "daHuGunFa": ["打虎棍法", 70, 3, 2, 23],
    "baWangJianFa": ["霸王剑法", 72, 4, 5, 22],
    "xiaoXiaoLuoMuDaoFa": ["萧萧落木刀法", 96, 4, 5, 21],
    "daLiJinGangGunFa": ["大力金刚棍法", 84, 3, 2, 23],
    "chaiJiaQiangFa": ["柴家枪法", 76, 4, 2, 26],
    "chuiYunPoFengQiangFa": ["吹云破风枪法", 76, 3, 3, 26],
    "chiMuDaoFa": ["赤目刀法", 84, 3, 2, 21],
    "ruoShuiDaoFa": ["弱水刀法", 45, 3, 2, 21],
    "yangJiaWuHeQiang": ["杨家五合枪", 45, 4, 3, 26],
    "maoShanDaoJian": ["茅山道剑", 42, 2, 2, 22],
    "tianJiJianFa": ["天机剑法", 45, 3, 4, 22],
    "zuiDao": ["醉刀", 75, 4, 1, 21],
    "daLiBanFu": ["大力板斧", 75, 2, 2, 24],
    "zhuFuJianFa": ["祝福剑法", 80, 2, 5, 22],
    "zhuFuDaoFa": ["祝福刀法", 80, 2, 5, 21],
    "kuaiGong": ["快攻", 30, 1, 1, 0],
    "daFuKanShu": ["大斧砍术", 10, 1, 1, 24],
    "ciQiangFa": ["刺枪法", 68, 3, 3, 26],
    "jingKanDaoFa": ["精砍刀法", 30, 3, 1, 21],
    "jingCiJianFa": ["精刺剑法", 72, 3, 2, 22],
    "suNvJianFa": ["素女剑法", 71, 2, 1, 22],
    "daLiGunFa": ["大力棍法", 84, 2, 2, 23],
    "daLiDaoFa": ["大力刀法", 84, 1, 1, 21],
    "chongFengQiangFa": ["冲锋枪法", 76, 4, 2, 26],
    "daLiJianFa": ["大力剑法", 73, 3, 3, 22],
    "jinLiYiJi": ["尽力一击", 84, 4, 2, 0],
    "tieJiaQiang": ["铁甲枪", 45, 4, 3, 26],
    "muOuJianFa": ["木偶剑法", 42, 3, 5, 22],
    "shuiZhongJianFa": ["水中剑法", 45, 3, 4, 22],
    "shenXingGunFa": ["神行棍法", 45, 2, 2, 23],
    "changBiDao": ["长臂刀", 75, 5, 5, 21],
    "daLiFu": ["大力斧", 56, 1, 1, 24],
    "huBenChuiFa": ["虎奔锤法", 75, 3, 4, 25],
    "huNuJianFa": ["虎怒剑法", 75, 3, 4, 22],
    "huPuDaoFa": ["虎扑刀法", 75, 3, 4, 21],
    "huChongFuFa": ["虎冲斧法", 75, 3, 4, 24],
    "huHouQiangFa": ["虎吼枪法", 75, 3, 4, 26],
    "luanJian": ["乱剑", 50, 5, 5, 22],
    "kuangBaoGun": ["狂暴棍", 60, 2, 2, 23],
    "junFu": ["军斧", 60, 2, 2, 24],
    "zhongJiaQiangFa": ["重甲枪法", 87, 3, 2, 26],
    "feiYingJianFa": ["飞影剑法", 30, 3, 3, 22],
    "zhongChui": ["重锤", 30, 3, 2, 25],
    "shenDaoFa": ["神刀法", 70, 2, 2, 21],
    "pinMingYiJi": ["拼命一击", 80, 1, 3, 0],
    "feiDaoFa": ["飞刀法", 30, 1, 1, 21],
    "yuLinJianFa": ["御林剑法", 51, 3, 5, 22],
    "yuLinDaoFa": ["羽林刀法", 54, 2, 2, 21],
    "huangJinQiangFa": ["黄金枪法", 90, 3, 3, 26],
    "feiTianShenJian": ["飞天神剑", 180, 1, 2, 22],
    "niangZiJianFa": ["娘子剑法", 56, 2, 2, 22],
    "feiXianJianFa": ["飞仙剑法", 80, 3, 5, 22],
    "longYinJianFa": ["龙吟剑法", 84, 3, 6, 0],
    "jinGuangCiYanChui": ["金光刺眼锤", 100, 2, 3, 25],
    "luanYanGunFa": ["乱眼棍法", 90, 1, 4, 23],
    "chiYunQiangFa": ["赤云枪法", 95, 4, 2, 26, 52],
    "hengSaoQianJun": ["横扫千军", 130, 3, 4, 24],
    "suiLangDaoFa": ["随浪刀法", 110, 4, 2, 21],
    "duBiJianFa": ["独臂剑法", 90, 4, 1, 22],
    "lieHuoZhuiJi": ["烈火追击", 80, 1, 1, 0],
    "qingLongYanYueZhan": ["青龙偃月斩", 208, 4, 4, 0],
    "baiBuChuanYang": ["百步穿杨", 228, 3, 4, 0],
    "xuanGuangShuiLei": ["炫光水雷", 300, 4, 4, 0],
    "zhanChe": ["战车", 208, 4, 4, 0],
    "eMiTuoFo": ["阿弥陀佛", 208, 4, 4, 0],
    "tiTianSuanMing": ["替天算命", 200, 4, 4, 0],
    "suXinJianFa": ["素心剑法", 208, 4, 4, 22],
    "zhanPao": ["战炮", 200, 4, 4, 0],
    "shenBian": ["神鞭", 200, 4, 4, 0],
    "shuangQiang": ["双枪", 200, 4, 4, 26],
    "xueYinShou": ["血印手", 180, 4, 5, 0],
    "jiangLongShiBaZhang": ["降龙十八掌", 200, 5, 10, 0],
    "daGouBangFa": ["打狗棒法", 180, 3, 5, 23],
    "ganJiangJianPu": ["干将剑谱", 1, 1, 1, 0, 300],
    "moXieJianPu": ["莫邪剑谱", 1, 1, 1, 0, 300],
    "zhanGeLiZan": ["战歌礼赞", 1, 1, 3, 0, 300],
    "naHan": ["呐喊", 1, 1, 2, 0, 300,],
    "qianJunShaWeiGunFa": ["千钧杀威棍法", 120, 4, 5, 23, 90,'融汇重力与杀气的绝杀技，棍势如陨石坠地。第二式“千钧一发“可压弯城门，第四式“万钧之力“能劈开山涧。岳飞曾用此棍斩断金军帅旗，发髻散落时棍气凝成“还我河山“四字，至今仍镌刻在风波亭石碑上'],
    "qianDaoWanGua": ["千刀万剐", 172, 3, 3, 21, 60,'凌迟酷刑般的连续切割，刀刀专攻人体脆弱处。刀诀云：“千刀取骨，万剐剥皮。“明朝锦衣卫用此法处决大太监刘谨，三千六百刀后皮肉寸寸剥离，露出森森白骨，刀刃磨损过半仍滴血不沾'],
    // 名称/伤害系数/攻击距离/攻击数量/武器类型/恢复时间
}
//兵器类别
const weaponType = {
    21: '刀',
    22: '剑',
    23: '棍',
    24: '斧',
    25: '锤',
    26: '枪',
    0: '武器'
}
// 兵种模板 步兵
const soldierTemplate = {
    //步兵
    bubing: {
        type: 'bubing',
        name: '步兵',
        attack: 10,
        defense: 20,
        hp: 520,
        addattack: 10,//升一级增加的攻击力
        adddefense: 20,//升一级增加的防御力
        addhp: 20,//升一级增加的生命值
        desc: '此兵种可以克制乱剑手，对乱剑手的伤害加倍',
        recruitFood: 20,//兵种招募粮草
        recruitGold: 20,//兵种招募银两
        recruitTime: 20,//兵种招募时间
        population: 1,//占用人口
        skill: [skillTemplate.puTongGongJi, skillTemplate.kuaiGong],
        isInit: false,//是否初始兵种
    },
    //金甲女兵
    jinjianvbing: {
        type: 'jinjianvbing',
        name: '金剑女兵',
        attack: 13,
        defense: 14,
        hp: 520,
        addattack: 10,//升一级增加的攻击力
        adddefense: 20,//升一级增加的防御力
        addhp: 20,//升一级增加的生命值
        desc: '此兵种可以克制禁军侍卫，对禁军侍卫的伤害加倍。',
        recruitFood: 20,//兵种招募粮草
        recruitGold: 20,//兵种招募银两
        recruitTime: 20,//兵种招募时间
        population: 1,//占用人口
        skill: [skillTemplate.puTongGongJi, skillTemplate.suNvJianFa],
        isInit: true,//是否初始兵种
    },
    //刀斧手
    daoFuShou: {
        type: 'daoFuShou',
        name: '刀斧手',
        attack: 13,//初始攻击力
        defense: 14,//初始防御力
        hp: 624,//初始生命值
        addattack: 13,//升一级增加的攻击力
        adddefense: 14,//升一级增加的防御力
        addhp: 20,//升一级增加的生命值
        desc: '(克制军斧兵)刀斧手整齐列阵，手中刀斧泛着寒光。他们训练有素，进攻时动作划一，刀斧齐下，威力惊人，是战场上执行强攻任务的可靠力量',
        recruitFood: 30,//兵种招募粮草
        recruitGold: 30,//兵种招募银两
        recruitTime: 24,//兵种招募时间
        population: 1,//占用人口
        skill: [skillTemplate.puTongGongJi, skillTemplate.daDaoKanShu, skillTemplate.daFuKanShu],
        isInit: false,//是否初始兵种
    },
    //乱棍军
    luanGongJun: {
        type: 'luanGongJun',
        name: '乱棍军',
        attack: 10,//初始攻击力
        defense: 20,//初始防御力
        hp: 520,//初始生命值
        addattack: 10,//升一级增加的攻击力
        adddefense: 20,//升一级增加的防御力
        addhp: 20,//升一级增加的生命值
        desc: '(克制骑兵)乱棍军以杂乱却又极具威力的棍法闻名。士兵们手持长短不一的木棍，相互配合，乱中有序，在战场上搅乱敌阵，令敌人难以招架',
        recruitFood: 30,//兵种招募粮草
        recruitGold: 30,//兵种招募银两
        recruitTime: 24,//兵种招募时间
        population: 1,//占用人口
        skill: [skillTemplate.puTongGongJi, skillTemplate.luanGun],
        isInit: false,//是否初始兵种
    }
}
//武将模板
const generalTemplate = {
    //周武师
    zhouwushi: {
        name: '周武师',
        skill: [skillTemplate.puTongGongJi, skillTemplate.daDaoKanShu, skillTemplate.naHan],
        soldier: [soldierTemplate.bubing],
    },
    //史进
    shijin: {
        name: '史进',
        skill: [skillTemplate.puTongGongJi, skillTemplate.yanLingDaoFa, skillTemplate.qianJunShaWeiGunFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.daoFuShou],
    },
    //王进
    wangjin: {
        name: '王进',
        skill: [skillTemplate.puTongGongJi, skillTemplate.luanGun, skillTemplate.daFuKanShu],
        soldier: [soldierTemplate.bubing, soldierTemplate.luanGongJun],
    }
}
function isPureNumber(str) {
    return /^\d+$/.test(str);
}
//返回物品类型
function getGoodType(type, subType) {
    let str = '',
        typeArr = {
            1: '消耗品', 2: '兵器', 3: '防具', 4: '士兵兵器', 5: '士兵防具', 6: '坐骑装备', 7: '装备宝石', 8: '其它'
        },
        subTypeArr = {
            21: '刀', 22: '剑', 23: '棍', 24: '斧', 25: '锤', 26: '枪',
            31: '帽子', 32: '衣服', 33: '裤子', 34: '鞋子', 35: '项链', 36: '戒指', 37: '手套', 38: '肩甲', 39: '披风', 40: '特殊戒指', 41: '左手套', 42: 'PK盾',
            61: '蹄铁', 62: '马鞍', 63: '缰绳', 64: '马铠', 65: '马蹬', 66: '马嚼',
            71: '商城', 72: '强化', 73: '材料', 74: '训练', 75: '任务', 76: '活动', 77: '神兵', 78: '其它'
        };
    str += typeArr[type];
    subType && (str += '·' + subTypeArr[subType])
    return str
}
//根据等级计算初始血量
function getHpByLevel(level) {
    const fnExp = () => {
        let sum = 0, a = 1;
        for (let i = 1; i <= level; i++) {
            sum += a;
            a += 2;
        }
        return sum
    }
    return 2600 + level * 100 + fnExp()
}
// 角色升级所需资源计算公式 时间返回为秒
function roleUpgrade(start: number, end = start + 1) {
    const fnExp = (to) => {
        let a = 100;
        let result = 0
        for (let i = 2; i <= to; i++) {
            a += i * 200 - 100
            result += a;
        }
        return result
    }
    let resource = fnExp(end) - fnExp(start)
    let { time, pot } = roleUpgrade1(start, end)
    // let potential=time*
    return { resource, time, pot }
}
//传入资源返回最大升级多少
function checkMaxLevel(start: number, resource1: number, pot1: number) {
    let end = start
    for (let index = 1; index < 200; index++) {
        end=start+index
        let { resource, pot } = roleUpgrade(start, end)
        if (resource1 < resource || pot1 < pot) {
            let { resource, pot } = roleUpgrade(start, end - 1)
            return { end, resource, pot }
        }
    }
}
// 角色升级所需时间计算公式
function roleUpgrade1(start: number, end = start + 1) {
    const fnExp = (to: number) => {
        let a = -2;
        let result = 0
        for (let i = 2; i <= to; i++) {
            a += (i * 4)
            result += a;
        }
        return result
    }
    const fn = (to: number) => {
        let result = 0
        let pot = 0
        for (let i = 1; i <= to; i++) {
            let a = fnExp(i)
            result += a
            pot += (Math.ceil(i / 2) * a)
        }
        return { time: result, pot }
    }
    let { time: time1, pot: pot1 } = fn(start)
    let { time: time2, pot: pot2 } = fn(end)
    let result = time2 - time1
    let pot = pot2 - pot1
    return { time: result, pot }
}
//房屋和人口计算
function manorPopulation(level) {
    return 10 + level* 20
}
//经脉处理
//冲脉、带脉、阴维脉、阳维脉、阴跷脉、阳跷脉、任脉、督脉
/**
 * 
 * @param name 已开脉名称
 * @returns 已开脉字符串和未开脉对象
 */
function jingmaiFn(name) {
    //粮草和潜能使用一样多
    let ziyuan: any = {
        '冲脉': { potential: 12100000, longdan: 1 },
        '带脉': { potential: 12100000, longdan: 2 },
        '阴维脉': { potential: 12100000, longdan: 4 },
        '阳维脉': { potential: 12100000, longdan: 6 },
        '阴跷脉': { potential: 12100000, longdan: 8 },
        '阳跷脉': { potential: 12100000, longdan: 10 },
        '任脉': { potential: 12100000, longdan: 15 },
        '督脉': { potential: 12100000, longdan: 30 },
    }
    let str = '', obj: any = {}
    let markBool = false;
    for (let key in ziyuan) {
        if (markBool || !name) {
            obj = JSON.parse(JSON.stringify(ziyuan[key]))
            obj.value = `打通${key}需要龙灵仙凤丹x${ziyuan[key].longdan}、粮食x${ziyuan[key].potential}、潜能x${ziyuan[key].potential}，你确定要打通吗？`
            obj.name = key
            obj.longdanNum = ziyuan[key].longdan
            obj.populationNum = ziyuan[key].potential
            break;
        }
        if (key == name) {
            markBool = true
            str += key
        }
    }
    return { opened: str || '无', notOpen: obj }
}
//开启魂魄 金木水火土
function hunpofn(name) {
    let ziyuan = {
        '金': { potential: 133100000, hunpoNum: 10, hunpoId: 41 },
        '木': { potential: 172800000, hunpoNum: 12, hunpoId: 42 },
        '水': { potential: 219700000, hunpoNum: 14, hunpoId: 43 },
        '火': { potential: 274400000, hunpoNum: 16, hunpoId: 44 },
        '土': { potential: 337500000, hunpoNum: 20, hunpoId: 45 },
    }
    let str = '', obj: any = {}
    let markBool = false;
    for (let key in ziyuan) {
        if (markBool || !name) {
            obj = JSON.parse(JSON.stringify(ziyuan[key]))
            obj.value = `打通魂魄[${key}]需要魂魄灵珠[${key}]x10、潜能x${ziyuan[key].potential}、粮食x${ziyuan[key].potential}、木材x${ziyuan[key].potential}、生铁x${ziyuan[key].potential}、石料x${ziyuan[key].potential}，你确定要打通吗？`
            obj.name = key//名称
            obj.hunpoNum = ziyuan[key].hunpoNum//魂魄数量
            obj.hunpoId = ziyuan[key].hunpoId//魂魄id
            obj.populationNum = ziyuan[key].potential//资源
            break;
        }
        if (key == name) {
            markBool = true
            str += key
        }
    }
    return { opened: str || '无', notOpen: obj }
}
const handelfn:any={}
handelfn.jingmai=(name:string)=>{
    let ziyuan: any = {
        '冲脉': { gongji: 0.05, fangyu: 0.01 },
        '带脉': { gongji: 0.1, fangyu: 0.02 },
        '阴维脉': { gongji: 0.16, fangyu: 0.03 },
        '阳维脉': { gongji: 0.22, fangyu: 0.04 },
        '阴跷脉': { gongji: 0.28, fangyu: 0.05 },
        '阳跷脉': { gongji: 0.36, fangyu: 0.07 },
        '任脉': { gongji: 0.50, fangyu: 0.09 },
        '督脉': { gongji: 0.69, fangyu: 0.11 },
    };
    return ziyuan[name]
}
handelfn.hunpo=(name:string)=>{
    let ziyuan: any = {
        '金': { gongji: 0.05, fangyu: 0.01 },
        '木': { gongji: 0.1, fangyu: 0.03 },
        '水': { gongji: 0.16, fangyu: 0.06 },
        '火': { gongji: 0.22, fangyu: 0.09 },
        '土': { gongji: 0.28, fangyu: 0.13 },
    }
    return ziyuan[name]
}
export {
    handelfn,
    hunpofn,
    jingmaiFn,
    buildType,
    manorPopulation,
    handlbuildfn,
    getHpByLevel,
    roleUpgrade,
    checkMaxLevel,
    extractBetween,
    goodsType,
    weaponType,
    formatDate,
    defaultConfig,
    filterRouter,
    buildconfig,
    formatDuring,
    manorEventfn,
    generalTemplate,
    isPureNumber,
    getGoodType,
    soldierTemplate,
    getRandomInt,
    getRandomNumber,
    equipmentMountType,
    goodsType1,
    equipmentType
}