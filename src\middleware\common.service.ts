import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ManorEntity } from 'src/entities/manor.entity';
import { RoleEntity } from 'src/entities/role.entity';
import { manorPopulation } from 'src/utils/config';
import { DataType, UserInfoType } from 'src/utils/types';
import { DataSource, Repository } from 'typeorm';
import { RedisService } from './redis.service';
import { GeneralEntity } from 'src/entities/general.entity';
import { SoldierEntity } from 'src/entities/soldier.entity';
import { NpcEntity } from 'src/entities/npc.entity';
import { RoleTasksEntity } from 'src/entities/roleTasks.entity';
import { GoodEntity } from 'src/entities/goods.entity';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
@Injectable()
export class CommonService {
    constructor(
        private readonly redisService: RedisService,
        @InjectRepository(RoleEntity) private readonly roleEntity:Repository<RoleEntity>,
        @InjectRepository(GeneralEntity) private readonly generalEntity: Repository<GeneralEntity>,
        @InjectRepository(ManorEntity) private readonly manorEntity: Repository<ManorEntity>,
        @InjectRepository(SoldierEntity) private readonly soldierEntity: Repository<SoldierEntity>,
        @InjectRepository(NpcEntity) private readonly npcEntity: Repository<NpcEntity>,
        @InjectRepository(RoleTasksEntity) private readonly roleTasksEntity: Repository<RoleTasksEntity>,
        @InjectRepository(GoodEntity) private readonly goodEntity: Repository<GoodEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        private dataSource: DataSource
    ) { }

    
    //用户快下线了 更新用户gps 清除用户在线状态
    async userOffline(userId: number) {
        let userInfo:RoleEntity=JSON.parse(await this.redisService.hget('user'+userId,'userInfo')||'{}');
        if(userInfo.gpsId){
            await this.roleEntity.save({id:userId,gpsId:userInfo.gpsId})//更新用户gps
            let users=JSON.parse(await this.redisService.hget('mapInfo','users'+userInfo.gpsId)||'[]')
            if(users.length){
                users=users.filter(item=>item.id!=userId)
                await this.redisService.hset('mapInfo','users'+userInfo.gpsId,JSON.stringify(users))//清除用户在线状态
            }
        }
        
        
    }
    //修改用户粮草 木材 石料 生铁 银两
    async updateUserResources(userId: number, resources: UserInfoType) {
        let userInfo=JSON.parse(await this.redisService.hget('user'+userId,'userInfo'))
        userInfo.food+=resources.food
        userInfo.wood+=resources.wood
        userInfo.stone+=resources.stone
        userInfo.iron+=resources.iron
        userInfo.silver+=resources.gold
        await this.roleEntity.save(userInfo)
        // await this.roleEntity.update(userId, { resources });
    }
    //返回爷爷路由
    /**
     * 
     * @param userId 用户id
     * @returns 
     */
    async getPrevRouter(userId) {
        //返回路由 
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        let result=routerList.slice(-3,-2)
        return result
    }
    //返回爷爷路由并删除最后一个路由
    async getPrevRouter2(userId) {
        //返回路由 
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        routerList=routerList.slice(0,-2)
        await this.redisService.hset('user' + userId, 'routerList', JSON.stringify(routerList))
        let result=routerList.pop()
        return result
    }
    //返回父亲路由并删除最后一个路由
    async getPrevRouter3(userId) {
        //返回路由 
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        routerList=routerList.filter(item=>item.params.hidden!=true)
        routerList=routerList.slice(0,-1)
        await this.redisService.hset('user' + userId, 'routerList', JSON.stringify(routerList))
        let result=routerList.pop()
        return result
    }
    async getPrevRouter1(userId) {
        let str = ''
        //返回路由 
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        routerList.reverse().shift()
        let result=routerList.shift() 
        return result
    }
    //返回 hidden不为true的路由
    async getNotHiddenRouter(userId) {
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        routerList.reverse()
        return routerList.filter(item => item.params.hidden != true)[0]
    }
    //统计角色最大人口
    async userMaxPopulation(userId:number) {
        let manorList=await this.manorEntity.find({where:{userId,type:2}})
        let maxPopulation=5
        manorList.forEach(item=>{
        maxPopulation+=manorPopulation(item.level)
        })
        return maxPopulation
    }
    //统计武将带兵
    async generalPopulation(generalId:number) {
        //什么时候要统计 武将升级/调离士兵/战斗/士兵人口变化
        let generalDetail = await this.generalEntity.findOne({
            where: { id: generalId },
            relations: ['soldiers']
        });
        let population=0,count=0,countAll=0;
        generalDetail.soldiers.forEach(item=>{
            population+=item.count
            count+=(item.population*item.count)
        })
        countAll=8 + generalDetail.level * 3
        //勋章待计算
        await this.generalEntity.update(generalId,{population:population,count:count,countAll:countAll})
    }
    //移动
    /**
     * 
     * @param userId 用户id
     * @param oldGpsId 当前地图id
     * @param gpsId 要移动的地图id
     * @param userName 用户名称
     */
    async userMove(userId: number,oldGpsId:number,gpsId:number,userName:string) {
        //更新用户gps信息，把上个地图的用户删除，在当前地图的用户添加
        if (oldGpsId&&oldGpsId != gpsId) {
            await this.roleEntity.update({ id: userId }, { gpsId: gpsId })
            let users = JSON.parse((await this.redisService.hget('mapInfo', 'users' + oldGpsId)) || '[]')
            users = users.filter(item => item.id != userId)
            await this.redisService.hset('mapInfo', 'users' + oldGpsId, JSON.stringify(users))
            users = JSON.parse((await this.redisService.hget('mapInfo', 'users' + gpsId) || '[]'))
            if(!users.find(item=>item.id==userId)){
                users.push({ id: userId, name: userName })
                await this.redisService.hset('mapInfo', 'users' + gpsId, JSON.stringify(users))
            }
            let userInfo=await this.roleEntity.findOne({where:{id:userId}})
            await this.redisService.hset('user' + userId, 'userInfo', JSON.stringify(userInfo))
        }
    }
    //初始化链接
    async initLink(sid: string, cmd: number, userId: string,service:string, params1:any,pageTitle='',name='') {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name, title:pageTitle, service, params: params1 } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        return {
            params,
            urlObj,
            backRouter,
        }
    }
    //设置返回路由
    async backRouter(userId, params, urlObj) {
        let str = ''
        //返回路由
        let routerList = JSON.parse(await this.redisService.hget('user' + userId, 'routerList'))
        routerList.reverse().shift()
        routerList.forEach(element => {
            if(!element.params.hidden){
                str += `<a href="${this.seturlOther(params, urlObj, element.title, element.name, element.params, element.service)}">返回${element.title}</a><br/>`
            }
        });
        return str 
    }
    seturlOther(params, urlObj, title: string, name: string, attr = {}, service) {
        params.cmd++
        urlObj[params.cmd] = { title, name, service, params: attr }
        return `/gcmd?sid=${params.sid}&cmd=${params.cmd}`
    }
    //获取庄园信息
    async getManorInfo(userId: string, type?: number) {
        let manorInfo = await this.manorEntity.find({ where: { userId: Number(userId) } });
        if (type) {
            manorInfo = manorInfo.filter(item => item.type == type)
        }
        return manorInfo
    }
    //获取武将列表
    async getGeneralList(userId: string) {
        return await this.generalEntity.find({ where: { userId: Number(userId) } });
    }
    //获取士兵列表
    async getSoldierList(userId: string) {
        return await this.soldierEntity.find({ where: { userId: Number(userId) } });
    }
    //获取武将详情 关联士兵 技能
    async getGeneralDetail(generalId: number) {
        let generalDetail = await this.generalEntity.findOne({
            where: { id: generalId },
            relations: ['skills', 'soldiers', 'soldiers.skills', 'soldiers.equip']
        });
        // generalDetail.equip=await this.getEquipDetail(generalId)
        return generalDetail
    }
    //获取npc详情
    async getNpcInfo(npcId: number) {
        let npcInfo = await this.redisService.hget('npcInfo', String(npcId))
        if(npcInfo==null||npcInfo=='null'){
            let npcInfo = await this.npcEntity.findOne({ where: { id: npcId }, relations: ['goods','npcGoods', 'generals', 'generals.soldiers'] })
            await this.redisService.hset('npcInfo', String(npcId), JSON.stringify(npcInfo))
            return npcInfo
        }else{
            return JSON.parse(npcInfo)
        }
    }
    
    //获取用户任务列表
    async getUserTaskList(userId: number) {
        let taskList=await this.redisService.hget('user'+userId, 'taskList')
        if(taskList==null||taskList=='null'){
            let taskList = await this.roleTasksEntity.find({ where: {role:{id: userId}} })
            await this.redisService.hset('user'+userId, 'taskList', JSON.stringify(taskList))
            return taskList
        }else{
            return JSON.parse(taskList)
        }
    }
    //根据物品名称获取物品id
    async getGoodIdByName(goodName: string) {
        let goodInfo:any=this.redisService.hget('goodInfo', goodName)
        if(goodInfo){
            goodInfo=JSON.parse(goodInfo)
        }else{
            goodInfo=await this.goodEntity.findOne({ where: { name: goodName } })
        }
        return goodInfo.id
    }
    //根据物品名称返回玩家物品实体
    async getPersonGoodByName(userId: number, goodName: string) {
        return await this.personGoodsEntity.findOne({ where: { userId, good: { name: goodName } },select:['id','count'] })
    }
    
}
