import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn } from 'typeorm'
//聊天记录表
@Entity()
export class ChatEntity {

    @PrimaryGeneratedColumn()
    id: number

    @Column({ type: 'varchar', length: 20, nullable: true })
    userName: string
    //发送人id
    @Column({ type: 'int', nullable: true })
    userId: number
    //聊天内容
    @Column({ type: 'varchar', length: 200 })
    content: string
    //消息类型 公聊0   帮派1  队伍2 系统3 私聊4
    @Column({ type: 'int',default:4 })
    type: number
    //如果是私聊 接受者id
    @Column({ type: 'int', nullable: true })
    toUserId: number

    //创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;
}